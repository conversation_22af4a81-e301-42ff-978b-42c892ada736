-- 初始化数据库脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS health_uplink 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- =================================================================
-- 创建用户并授权
-- 注意: 在生产环境中，建议为 'health_user' 设置一个更强的密码。
-- =================================================================
CREATE USER IF NOT EXISTS 'health_user'@'%' IDENTIFIED BY 'health_user';
GRANT ALL PRIVILEGES ON health_uplink.* TO 'health_user'@'%';

-- 使用数据库
USE health_uplink;

-- 创建患者表
CREATE TABLE IF NOT EXISTS patients (
    patient_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '患者姓名',
    age INT NOT NULL COMMENT '年龄',
    medical_history TEXT COMMENT '病史',
    symptoms TEXT COMMENT '症状',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者信息表';

-- 创建图片表
CREATE TABLE IF NOT EXISTS images (
    image_id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) COMMENT '文件名',
    original_name VARCHAR(255) COMMENT '原始文件名',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_size INT COMMENT '文件大小（字节）',
    mime_type VARCHAR(100) COMMENT '文件类型',
    patient_id INT COMMENT '关联患者ID',
    image_url VARCHAR(255) NOT NULL COMMENT '图片URL',
    image_type VARCHAR(50) COMMENT '图片类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    INDEX idx_patient_id (patient_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片信息表';

-- 创建医生表
CREATE TABLE IF NOT EXISTS doctors (
    doctor_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    name VARCHAR(100) NOT NULL COMMENT '医生姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    department VARCHAR(100) COMMENT '科室',
    title VARCHAR(50) COMMENT '职称',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生信息表';

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource VARCHAR(100) COMMENT '操作资源',
    resource_id INT COMMENT '资源ID',
    details TEXT COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    config_id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('system_name', 'Health Uplink', '系统名称'),
('version', '1.0.0', '系统版本'),
('max_upload_size', '10485760', '最大上传文件大小（字节）'),
('allowed_file_types', 'jpg,jpeg,png,gif,pdf', '允许上传的文件类型'),
('backup_retention_days', '30', '备份保留天数'),
('session_timeout', '3600', '会话超时时间（秒）')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = CURRENT_TIMESTAMP;

-- 创建视图：患者统计
CREATE OR REPLACE VIEW patient_statistics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as patient_count,
    AVG(age) as avg_age,
    MIN(age) as min_age,
    MAX(age) as max_age
FROM patients 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 创建视图：图片统计
CREATE OR REPLACE VIEW image_statistics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as image_count,
    SUM(file_size) as total_size,
    AVG(file_size) as avg_size
FROM images 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- =================================================================
-- 授予备份所需的额外权限
-- =================================================================
-- 为 'root'@'%' 用户授予 RELOAD 和 FLUSH_TABLES 权限
-- 注意: 在生产环境中，建议创建一个专用的备份用户，而不是使用root。
GRANT RELOAD, FLUSH_TABLES ON *.* TO 'root'@'%';

-- 刷新权限使更改生效
FLUSH PRIVILEGES;
