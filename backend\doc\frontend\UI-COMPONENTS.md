# Health Uplink UI 组件库使用指南

本文档介绍了 Health Uplink 后端管理系统的标准 UI 组件库和样式规范。

## 📋 目录

- [设计系统](#设计系统)
- [标准组件](#标准组件)
- [使用示例](#使用示例)
- [样式规范](#样式规范)
- [最佳实践](#最佳实践)

## 🎨 设计系统

### 颜色系统

```css
/* 主色调 */
--primary-color: #1890ff;
--primary-light: #40a9ff;
--primary-dark: #096dd9;

/* 辅助色 */
--success-color: #52c41a;
--warning-color: #faad14;
--error-color: #ff4d4f;
--info-color: #1890ff;

/* 中性色 */
--text-primary: #262626;
--text-secondary: #595959;
--text-tertiary: #8c8c8c;
--text-disabled: #bfbfbf;

/* 背景色 */
--bg-primary: #ffffff;
--bg-secondary: #fafafa;
--bg-tertiary: #f5f5f5;
```

### 间距系统

```css
--spacing-xs: 4px;   /* 极小间距 */
--spacing-sm: 8px;   /* 小间距 */
--spacing-md: 16px;  /* 中等间距 */
--spacing-lg: 24px;  /* 大间距 */
--spacing-xl: 32px;  /* 超大间距 */
--spacing-xxl: 48px; /* 极大间距 */
```

### 字体系统

```css
--font-size-xs: 12px;   /* 辅助文字 */
--font-size-sm: 14px;   /* 正文（小） */
--font-size-md: 16px;   /* 正文 */
--font-size-lg: 18px;   /* 小标题 */
--font-size-xl: 20px;   /* 标题 */
--font-size-xxl: 24px;  /* 主标题 */
```

## 🧩 标准组件

### 1. ContentContainer - 页面容器

页面的基础容器组件，提供统一的页面布局和样式。

```vue
<template>
  <ContentContainer 
    title="页面标题"
    description="页面描述信息"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 页面内容 -->
  </ContentContainer>
</template>
```

**Props:**
- `title` - 页面标题
- `description` - 页面描述
- `showHeader` - 是否显示头部
- `showBreadcrumb` - 是否显示面包屑
- `breadcrumbs` - 面包屑数据

### 2. SearchForm - 搜索表单

标准化的搜索表单组件，支持多种字段类型。

```vue
<template>
  <SearchForm
    :search-fields="searchFields"
    :loading="loading"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script>
const searchFields = [
  {
    key: 'name',
    label: '姓名',
    type: 'input',
    placeholder: '请输入姓名',
    clearable: true
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    key: 'date_range',
    label: '创建时间',
    type: 'daterange',
    placeholder: '选择时间范围'
  }
]
</script>
```

**支持的字段类型:**
- `input` - 输入框
- `select` - 选择器
- `date` - 日期选择器
- `daterange` - 日期范围选择器

### 3. DataTable - 数据表格

功能完整的数据表格组件，集成分页、排序、操作等功能。

```vue
<template>
  <DataTable
    :data="tableData"
    :columns="tableColumns"
    :loading="loading"
    :pagination="pagination"
    title="数据列表"
    @current-change="handleCurrentChange"
    @size-change="handleSizeChange"
    @refresh="fetchData"
  >
    <!-- 自定义操作列 -->
    <template #actions="{ row }">
      <el-button type="text" @click="handleEdit(row)">编辑</el-button>
      <el-button type="text" @click="handleDelete(row)">删除</el-button>
    </template>
  </DataTable>
</template>

<script>
const tableColumns = [
  {
    prop: 'id',
    label: 'ID',
    width: 80,
    align: 'center'
  },
  {
    prop: 'name',
    label: '姓名',
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    type: 'status',
    statusMap: {
      1: { text: '启用', type: 'success' },
      0: { text: '禁用', type: 'danger' }
    }
  }
]
</script>
```

### 4. StandardPagination - 分页组件

标准化的分页组件，提供一致的分页体验。

```vue
<template>
  <StandardPagination
    :current-page="pagination.page"
    :page-size="pagination.pageSize"
    :total="pagination.total"
    :page-sizes="[10, 20, 50, 100]"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>
```

### 5. StandardForm - 标准表单

动态表单组件，支持多种表单控件类型。

```vue
<template>
  <StandardForm
    :form-fields="formFields"
    :form-model="formModel"
    :form-rules="formRules"
    @submit="handleSubmit"
    @reset="handleReset"
  />
</template>

<script>
const formFields = [
  {
    prop: 'name',
    label: '姓名',
    type: 'input',
    placeholder: '请输入姓名',
    required: true
  },
  {
    prop: 'age',
    label: '年龄',
    type: 'number',
    min: 0,
    max: 150
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    rows: 4
  }
]
</script>
```

## 💡 使用示例

### 完整页面示例

```vue
<template>
  <ContentContainer 
    title="用户管理"
    description="管理系统用户信息"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 搜索区域 -->
    <SearchForm
      :search-fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      :data="users"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      title="用户列表"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      @refresh="fetchUsers"
    >
      <template #toolbar-right>
        <el-button type="primary" @click="handleAdd">
          新增用户
        </el-button>
      </template>

      <template #actions="{ row }">
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-button type="text" @click="handleDelete(row)">删除</el-button>
      </template>
    </DataTable>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue'
import ContentContainer from '@/components/ContentContainer.vue'
import SearchForm from '@/components/SearchForm.vue'
import DataTable from '@/components/DataTable.vue'

export default defineComponent({
  components: {
    ContentContainer,
    SearchForm,
    DataTable
  },
  setup() {
    const users = ref([])
    const loading = ref(false)
    const pagination = ref({
      page: 1,
      pageSize: 10,
      total: 0
    })

    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '用户管理', path: '/users' }
    ])

    const searchFields = computed(() => [
      {
        key: 'name',
        label: '用户名',
        type: 'input',
        placeholder: '请输入用户名'
      },
      {
        key: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    ])

    const tableColumns = computed(() => [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'name', label: '用户名', minWidth: 120 },
      { prop: 'email', label: '邮箱', minWidth: 150 },
      { 
        prop: 'status', 
        label: '状态', 
        width: 100,
        type: 'status',
        statusMap: {
          1: { text: '启用', type: 'success' },
          0: { text: '禁用', type: 'danger' }
        }
      }
    ])

    const fetchUsers = async () => {
      // 获取用户数据的逻辑
    }

    const handleSearch = (searchData) => {
      // 搜索处理逻辑
    }

    const handleReset = () => {
      // 重置处理逻辑
    }

    // 其他事件处理函数...

    onMounted(() => {
      fetchUsers()
    })

    return {
      users,
      loading,
      pagination,
      breadcrumbs,
      searchFields,
      tableColumns,
      fetchUsers,
      handleSearch,
      handleReset
      // 其他返回值...
    }
  }
})
</script>
```

## 📐 样式规范

### CSS 类命名规范

使用 BEM (Block Element Modifier) 命名规范：

```css
/* 块 */
.search-form { }

/* 元素 */
.search-form__input { }
.search-form__button { }

/* 修饰符 */
.search-form--compact { }
.search-form__button--primary { }
```

### 工具类使用

系统提供了丰富的工具类，可以快速应用样式：

```html
<!-- 间距 -->
<div class="m-md p-lg">内容</div>

<!-- 文字 -->
<span class="text-primary font-medium">主要文字</span>
<span class="text-secondary text-sm">次要文字</span>

<!-- 布局 -->
<div class="flex items-center justify-between">
  <span>左侧内容</span>
  <span>右侧内容</span>
</div>

<!-- 背景和圆角 -->
<div class="bg-primary rounded-lg shadow-md">卡片内容</div>
```

## ✅ 最佳实践

### 1. 组件使用原则

- **一致性**: 在整个应用中使用统一的组件和样式
- **可复用性**: 优先使用标准组件，避免重复造轮子
- **可维护性**: 遵循组件的 API 设计，不要随意修改组件内部样式

### 2. 样式编写规范

- 使用 CSS 变量而不是硬编码的颜色值
- 优先使用工具类，减少自定义样式
- 遵循响应式设计原则

### 3. 性能优化

- 合理使用 `v-show` 和 `v-if`
- 大数据列表使用虚拟滚动
- 图片懒加载和压缩

### 4. 无障碍访问

- 为交互元素添加适当的 `aria-label`
- 确保键盘导航的可用性
- 保持足够的颜色对比度

## 🔧 自定义扩展

如果标准组件无法满足需求，可以通过以下方式扩展：

### 1. 插槽扩展

```vue
<DataTable>
  <template #toolbar-right>
    <!-- 自定义工具栏内容 -->
  </template>
  
  <template #custom-column="{ row }">
    <!-- 自定义列内容 -->
  </template>
</DataTable>
```

### 2. 样式覆盖

```vue
<style scoped>
:deep(.el-table) {
  /* 覆盖 Element Plus 样式 */
}

.custom-table {
  /* 自定义样式 */
}
</style>
```

### 3. 组件继承

```vue
<script>
import DataTable from '@/components/DataTable.vue'

export default {
  extends: DataTable,
  // 扩展功能
}
</script>
```

---

通过遵循本指南，可以确保整个后端管理系统具有一致的视觉风格和用户体验。如有疑问或建议，请联系开发团队。
