const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// --- 环境变量加载逻辑 ---
// 确定环境
const NODE_ENV = process.env.NODE_ENV || 'development';

// 定义 .env 文件加载顺序
const envFiles = [
  `.env.${NODE_ENV}.local`,
  `.env.${NODE_ENV}`,
  '.env.local',
  '.env'
];

console.log(`[SEED] 当前环境 (NODE_ENV): ${NODE_ENV}`);
console.log(`[SEED] 将按以下顺序加载 .env 文件:`, envFiles);

envFiles.forEach(file => {
  // 注意: 脚本在 scripts/ 目录下，所以路径需要回退到 backend/
  const filePath = path.resolve(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`[SEED] 正在加载: ${file}`);
    dotenv.config({
      path: filePath,
      override: true
    });
  }
});
// --- 环境变量加载逻辑结束 ---
const bcrypt = require('bcryptjs');
const { Doctor, sequelize } = require('../models');

async function seedAdmin() {
  try {

    const adminUsername = process.env.DEFAULT_ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'password';

    // 检查 admin 用户是否已存在
    const existingAdmin = await Doctor.findOne({ where: { username: adminUsername } });

    if (existingAdmin) {
      console.log('Admin user already exists.');
      return;
    }

    // 创建 admin 用户
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    await Doctor.create({
      username: adminUsername,
      password_hash: hashedPassword,
      name: 'Administrator',
      email: '<EMAIL>',
      is_active: true
    });

    console.log('Admin user created successfully!');
    console.log(`Username: ${adminUsername}`);
    console.log(`Password: ${adminPassword}`);

  } catch (error) {
    console.error('Error seeding admin user:', error);
  } finally {
    await sequelize.close();
  }
}

seedAdmin();