import{_ as R,d as B,c as L,o as j,w as s,b as n,a as t,r as u,y as $,t as c,e as i,z as H,A as U,B as E,G as z,H as F,C as G,f as v,g as q,h as J,E as x}from"./index-D1pIs7PZ.js";const K=B({name:"SystemStatus",components:{ContentContainer:G,Monitor:F,DataLine:z,CircleCheck:E,Refresh:U,VideoPlay:H},setup(){const e=v(!1),l=v(!1),A=v({version:"1.0.0",environment:"development",uptime:"0s",nodeVersion:"N/A",startTime:"N/A"}),b=v({memory:"N/A",activeConnections:0,responseTime:"N/A",errorRate:"0%",lastUpdate:"N/A"}),h=v({text:"检查中...",class:"status-warning"}),g=v({text:"检查中...",class:"status-warning"}),C=v({text:"正常",class:"status-success"}),p=q(()=>h.value.class==="status-success"&&(g.value.class==="status-success"||g.value.class==="status-warning")?{text:"正常",class:"status-success"}:h.value.class==="status-error"?{text:"异常",class:"status-error"}:{text:"部分异常",class:"status-warning"}),r=v([{name:"后端API连接",status:"pending",statusText:"待测试",message:"",duration:""},{name:"健康检查端点",status:"pending",statusText:"待测试",message:"",duration:""},{name:"监控端点",status:"pending",statusText:"待测试",message:"",duration:""},{name:"认证功能",status:"pending",statusText:"待测试",message:"",duration:""}]),_=async()=>{try{const a=Date.now(),o=await fetch("http://localhost:3000/"),m=Date.now()-a;o.ok?(h.value={text:"正常",class:"status-success"},b.value.responseTime=`${m}ms`):h.value={text:"异常",class:"status-error"}}catch{h.value={text:"离线",class:"status-error"}}},P=async()=>{try{(await(await fetch("http://localhost:3000/health")).json()).database==="connected"?g.value={text:"已连接",class:"status-success"}:g.value={text:"未连接",class:"status-warning"}}catch{g.value={text:"检查失败",class:"status-warning"}}},S=async()=>{e.value=!0;try{const o=await(await fetch("http://localhost:3000/monitoring/info")).json();A.value={version:o.version||"1.0.0",environment:o.environment||"development",uptime:M(o.uptime||0),nodeVersion:o.nodeVersion||"N/A",startTime:o.startTime||"N/A"}}catch{x.error("获取系统信息失败")}finally{e.value=!1}},T=async()=>{var a;e.value=!0;try{const m=await(await fetch("http://localhost:3000/monitoring/status")).json();b.value={memory:N(((a=m.memory)==null?void 0:a.heapUsed)||0),activeConnections:m.activeConnections||0,responseTime:b.value.responseTime,errorRate:"0%",lastUpdate:new Date().toLocaleTimeString()}}catch{x.error("获取性能信息失败")}finally{e.value=!1}},k=async a=>{const o=r.value.findIndex(d=>d.name===a.name);if(o===-1)return;r.value[o].status="pending",r.value[o].statusText="测试中...";const m=Date.now();try{let d;switch(a.name){case"后端API连接":d=await f();break;case"健康检查端点":d=await I();break;case"监控端点":d=await D();break;case"认证功能":d=await w();break;default:d={success:!1,message:"未知测试项目"}}const V=Date.now()-m;r.value[o]={...r.value[o],status:d.success?"success":"error",statusText:d.success?"通过":"失败",message:d.message,duration:`${V}ms`}}catch(d){const V=Date.now()-m;r.value[o]={...r.value[o],status:"error",statusText:"失败",message:d.message,duration:`${V}ms`}}},y=async()=>{l.value=!0;for(const a of r.value)await k(a);l.value=!1,x.success("所有测试完成")},f=async()=>{const a=await fetch("http://localhost:3000/");return a.ok?{success:!0,message:"后端API响应正常"}:{success:!1,message:`HTTP ${a.status}`}},I=async()=>{const a=await fetch("http://localhost:3000/health");return a.ok?{success:!0,message:`状态: ${(await a.json()).status}`}:{success:!1,message:`HTTP ${a.status}`}},D=async()=>{const a=await fetch("http://localhost:3000/monitoring/status");return a.ok?{success:!0,message:"监控端点正常"}:{success:!1,message:`HTTP ${a.status}`}},w=async()=>({success:!0,message:"认证功能可用（需要数据库）"}),M=a=>{const o=Math.floor(a/3600),m=Math.floor(a%3600/60),d=Math.floor(a%60);return`${o}h ${m}m ${d}s`},N=a=>`${(a/1024/1024).toFixed(2)} MB`;return J(async()=>{await _(),await P(),await S(),await T()}),{loading:e,testLoading:l,systemInfo:A,performanceInfo:b,backendStatus:h,databaseStatus:g,frontendStatus:C,overallStatus:p,testResults:r,refreshSystemInfo:S,refreshPerformance:T,runSingleTest:k,runAllTests:y}}}),O={class:"status-overview"},Q={class:"status-item"},W={class:"status-info"},X={class:"status-item"},Y={class:"status-info"},Z={class:"status-item"},tt={class:"status-info"},st={class:"status-item"},et={class:"status-info"},at={class:"status-details"},nt={class:"card-header"},ot={class:"card-header"},lt={class:"component-test"},rt={class:"card-header"};function ut(e,l,A,b,h,g){const C=u("Monitor"),p=u("el-icon"),r=u("el-card"),_=u("el-col"),P=u("DataLine"),S=u("CircleCheck"),T=u("el-row"),k=u("Refresh"),y=u("el-button"),f=u("el-descriptions-item"),I=u("el-descriptions"),D=u("VideoPlay"),w=u("el-table-column"),M=u("el-tag"),N=u("el-table"),a=u("ContentContainer");return j(),L(a,{title:"系统状态",description:"查看系统运行状态和各组件健康情况","show-header":!0},{default:s(()=>[n("div",O,[t(T,{gutter:20},{default:s(()=>[t(_,{span:6},{default:s(()=>[t(r,{class:"status-card"},{default:s(()=>[n("div",Q,[n("div",{class:$(["status-icon",e.backendStatus.class])},[t(p,null,{default:s(()=>[t(C)]),_:1})],2),n("div",W,[l[0]||(l[0]=n("h3",null,"后端服务",-1)),n("p",null,c(e.backendStatus.text),1)])])]),_:1})]),_:1}),t(_,{span:6},{default:s(()=>[t(r,{class:"status-card"},{default:s(()=>[n("div",X,[n("div",{class:$(["status-icon",e.databaseStatus.class])},[t(p,null,{default:s(()=>[t(P)]),_:1})],2),n("div",Y,[l[1]||(l[1]=n("h3",null,"数据库",-1)),n("p",null,c(e.databaseStatus.text),1)])])]),_:1})]),_:1}),t(_,{span:6},{default:s(()=>[t(r,{class:"status-card"},{default:s(()=>[n("div",Z,[n("div",{class:$(["status-icon",e.frontendStatus.class])},[t(p,null,{default:s(()=>[t(C)]),_:1})],2),n("div",tt,[l[2]||(l[2]=n("h3",null,"前端服务",-1)),n("p",null,c(e.frontendStatus.text),1)])])]),_:1})]),_:1}),t(_,{span:6},{default:s(()=>[t(r,{class:"status-card"},{default:s(()=>[n("div",st,[n("div",{class:$(["status-icon",e.overallStatus.class])},[t(p,null,{default:s(()=>[t(S)]),_:1})],2),n("div",et,[l[3]||(l[3]=n("h3",null,"整体状态",-1)),n("p",null,c(e.overallStatus.text),1)])])]),_:1})]),_:1})]),_:1})]),n("div",at,[t(T,{gutter:20},{default:s(()=>[t(_,{span:12},{default:s(()=>[t(r,null,{header:s(()=>[n("div",nt,[l[5]||(l[5]=n("span",null,"系统信息",-1)),t(y,{type:"text",onClick:e.refreshSystemInfo,loading:e.loading},{default:s(()=>[t(p,null,{default:s(()=>[t(k)]),_:1}),l[4]||(l[4]=i(" 刷新 "))]),_:1,__:[4]},8,["onClick","loading"])])]),default:s(()=>[t(I,{column:1,border:""},{default:s(()=>[t(f,{label:"后端版本"},{default:s(()=>[i(c(e.systemInfo.version),1)]),_:1}),t(f,{label:"运行环境"},{default:s(()=>[i(c(e.systemInfo.environment),1)]),_:1}),t(f,{label:"运行时间"},{default:s(()=>[i(c(e.systemInfo.uptime),1)]),_:1}),t(f,{label:"Node.js版本"},{default:s(()=>[i(c(e.systemInfo.nodeVersion),1)]),_:1}),t(f,{label:"启动时间"},{default:s(()=>[i(c(e.systemInfo.startTime),1)]),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:12},{default:s(()=>[t(r,null,{header:s(()=>[n("div",ot,[l[7]||(l[7]=n("span",null,"性能指标",-1)),t(y,{type:"text",onClick:e.refreshPerformance,loading:e.loading},{default:s(()=>[t(p,null,{default:s(()=>[t(k)]),_:1}),l[6]||(l[6]=i(" 刷新 "))]),_:1,__:[6]},8,["onClick","loading"])])]),default:s(()=>[t(I,{column:1,border:""},{default:s(()=>[t(f,{label:"内存使用"},{default:s(()=>[i(c(e.performanceInfo.memory),1)]),_:1}),t(f,{label:"活跃连接"},{default:s(()=>[i(c(e.performanceInfo.activeConnections),1)]),_:1}),t(f,{label:"响应时间"},{default:s(()=>[i(c(e.performanceInfo.responseTime),1)]),_:1}),t(f,{label:"错误率"},{default:s(()=>[i(c(e.performanceInfo.errorRate),1)]),_:1}),t(f,{label:"最后更新"},{default:s(()=>[i(c(e.performanceInfo.lastUpdate),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),n("div",lt,[t(r,null,{header:s(()=>[n("div",rt,[l[9]||(l[9]=n("span",null,"组件测试",-1)),t(y,{type:"primary",onClick:e.runAllTests,loading:e.testLoading},{default:s(()=>[t(p,null,{default:s(()=>[t(D)]),_:1}),l[8]||(l[8]=i(" 运行所有测试 "))]),_:1,__:[8]},8,["onClick","loading"])])]),default:s(()=>[t(N,{data:e.testResults,style:{width:"100%"}},{default:s(()=>[t(w,{prop:"name",label:"测试项目",width:"200"}),t(w,{prop:"status",label:"状态",width:"120"},{default:s(o=>[t(M,{type:o.row.status==="success"?"success":o.row.status==="error"?"danger":"warning"},{default:s(()=>[i(c(o.row.statusText),1)]),_:2},1032,["type"])]),_:1}),t(w,{prop:"message",label:"结果"}),t(w,{prop:"duration",label:"耗时",width:"100"}),t(w,{label:"操作",width:"120"},{default:s(o=>[t(y,{type:"text",size:"small",onClick:m=>e.runSingleTest(o.row)},{default:s(()=>l[10]||(l[10]=[i(" 重新测试 ")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1})}const it=R(K,[["render",ut],["__scopeId","data-v-3457f66b"]]);export{it as default};
