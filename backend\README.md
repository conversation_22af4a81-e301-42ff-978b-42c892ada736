## 环境配置 (Environment Configuration)

本项目的后端服务采用分层的 `.env` 文件系统进行配置，以实现在不同环境（本地开发、Docker）下的无缝切换。

### 配置文件加载顺序

系统会根据 `NODE_ENV` 环境变量，按照以下优先级顺序加载配置文件（后加载的会覆盖先加载的）：

1.  `.env.${NODE_ENV}.local` (例如: `.env.development.local`)
2.  `.env.${NODE_ENV}` (例如: `.env.development`)
3.  `.env.local`
4.  `.env`

### 本地开发 (Local Development)

1.  **运行服务**:
    ```bash
    npm install
    node app.js
    ```
2.  **默认配置**: 本地开发将自动加载 `.env.development` 文件。
3.  **私有配置**: 如果你需要覆盖默认的开发配置（例如，使用不同的数据库密码），请创建一个 `.env.local` 文件。此文件已被 git 忽略，不会被提交。
    ```env
    # backend/.env.local (示例)
    DB_PASSWORD=your_secret_password
    JWT_SECRET=another_local_secret
    ```

### Docker 环境 (Docker Environment)

1.  **运行服务**:
    ```bash
    docker-compose up --build
    ```
2.  **默认配置**: Docker 环境会将 `NODE_ENV` 设置为 `docker`，后端服务启动时会自动加载 `.env.docker` 文件。
3.  **私有配置**: 你同样可以使用 `.env.local` 文件来覆盖 Docker 的默认配置。