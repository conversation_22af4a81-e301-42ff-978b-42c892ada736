2025-06-22 22:53:02 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:53:02 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:53:02 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:53:02 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:53:02 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:53:02 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 22:55:39 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:219:14)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:219:14)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-22 23:03:34 error: MulterError: Unexpected field
    at wrappedFileFilter (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\index.js:40:19)
    at Multipart.<anonymous> (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:518:28)
    at HeaderParser.cb (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:200:10)
    at SBMH.push (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (D:\vscode\AIWXworkspace\health-uplink\backend\node_modules\busboy\lib\types\multipart.js:567:19)
    at writeOrBuffer (node:internal/streams/writable:572:12)
2025-06-30 18:17:45 error: Health check failed: connect ETIMEDOUT
2025-06-30 18:38:25 error: Health check failed: connect ETIMEDOUT
2025-06-30 18:42:46 error: Health check failed: connect ETIMEDOUT
2025-06-30 18:44:45 error: Health check failed: connect ETIMEDOUT
2025-07-01 00:00:09 error: Backup failed: Command failed: Command failed: bash "D:\vscode\AIWXworkspace\health-uplink\scripts\backup.sh" "health_db" "incremental"
<3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

Stderr: <3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

2025-07-01 11:47:17 error: 测试错误日志
2025-07-05 12:00:07 error: Backup failed: Command failed: Command failed: bash "D:\vscode\AIWXworkspace\health-uplink\scripts\backup.sh" "health_db" "incremental"
<3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

Stderr: <3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

2025-07-07 00:00:08 error: Backup failed: Command failed: Command failed: bash "D:\vscode\AIWXworkspace\health-uplink\scripts\backup.sh" "health_db" "incremental"
<3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

Stderr: <3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

2025-07-07 08:33:56 error: Health check failed: connect ETIMEDOUT
2025-07-07 12:00:03 error: Backup failed: Command failed: Command failed: bash "D:\vscode\AIWXworkspace\health-uplink\scripts\backup.sh" "health_db" "incremental"
<3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

Stderr: <3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

2025-07-07 16:00:03 error: Backup failed: Command failed: Command failed: bash "D:\vscode\AIWXworkspace\health-uplink\scripts\backup.sh" "health_db" "incremental"
<3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

Stderr: <3>WSL (10 - Relay) ERROR: CreateProcessCommon:640: execvpe(/bin/bash) failed: No such file or directory

