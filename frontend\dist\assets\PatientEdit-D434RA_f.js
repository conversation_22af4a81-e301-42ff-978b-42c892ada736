import{_ as E,d as N,c as D,o as V,w as a,b as y,j as U,k as q,r as s,a as o,e as u,C as M,f as h,q as P,g as T,p as z,h as S,s as $,E as d,u as j,i as I}from"./index-D1pIs7PZ.js";const L=N({name:"PatientEdit",components:{ContentContainer:M},setup(){const e=z(),t=j(),f=h(!1),c=h(!1),r=h({}),b=h(),l=P({name:"",age:null,medical_history:"",symptoms:""}),p={name:[{required:!0,message:"请输入患者姓名",trigger:"blur"},{min:2,max:50,message:"姓名长度在 2 到 50 个字符",trigger:"blur"}],age:[{required:!0,message:"请输入患者年龄",trigger:"blur"},{type:"number",min:0,max:150,message:"年龄必须在 0 到 150 之间",trigger:"blur"}]},m=T(()=>[{title:"首页",path:"/"},{title:"患者管理",path:"/patients"},{title:"患者详情",path:`/patient/${e.params.id}`},{title:"编辑患者",path:`/patient/${e.params.id}/edit`}]),_=async()=>{f.value=!0;try{const n=await $.get(`/patient/${e.params.id}`);n.patient?(r.value=n.patient,l.name=n.patient.name||"",l.age=n.patient.age||null,l.medical_history=n.patient.medical_history||"",l.symptoms=n.patient.symptoms||""):d.error("获取患者详情失败")}catch(n){console.error("获取患者详情失败:",n),d.error("获取患者详情失败，请稍后重试")}finally{f.value=!1}},v=async()=>{try{if(!await b.value.validate())return;c.value=!0,(await $.put(`/patient/${e.params.id}`,l)).message?(d.success("患者信息更新成功"),t.push(`/patient/${e.params.id}`)):d.error("更新失败")}catch(n){console.error("更新患者信息失败:",n),d.error("更新失败，请稍后重试")}finally{c.value=!1}},w=async()=>{if(l.name!==(r.value.name||"")||l.age!==(r.value.age||null)||l.medical_history!==(r.value.medical_history||"")||l.symptoms!==(r.value.symptoms||""))try{await I.confirm("您有未保存的更改，确定要离开吗？","确认离开",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})}catch{return}t.push("/patients")},C=n=>n?new Date(n).toLocaleString("zh-CN"):"-";return S(()=>{_()}),{loading:f,saving:c,patient:r,formRef:b,formData:l,formRules:p,breadcrumbs:m,handleSave:v,handleCancel:w,formatDate:C}}}),A={class:"patient-edit-form"},F={class:"patient-history-section"};function G(e,t,f,c,r,b){const l=s("el-button"),p=s("el-input"),m=s("el-form-item"),_=s("el-col"),v=s("el-input-number"),w=s("el-row"),C=s("el-form"),n=s("el-card"),g=s("el-timeline-item"),k=s("el-timeline"),B=s("ContentContainer"),R=q("loading");return V(),D(B,{title:`编辑患者 - ${e.patient.name||"加载中..."}`,description:"编辑患者的基本信息和医疗记录","show-header":!0,breadcrumbs:e.breadcrumbs,"show-breadcrumb":!0},{"header-right":a(()=>[o(l,{onClick:e.handleCancel},{default:a(()=>t[4]||(t[4]=[u(" 取消 ")])),_:1,__:[4]},8,["onClick"]),o(l,{type:"primary",onClick:e.handleSave,loading:e.saving},{default:a(()=>t[5]||(t[5]=[u(" 保存 ")])),_:1,__:[5]},8,["onClick","loading"])]),default:a(()=>[y("div",A,[U((V(),D(n,null,{header:a(()=>t[6]||(t[6]=[y("span",null,"患者信息编辑",-1)])),default:a(()=>[o(C,{ref:"formRef",model:e.formData,rules:e.formRules,"label-width":"100px",size:"large"},{default:a(()=>[o(w,{gutter:20},{default:a(()=>[o(_,{span:12},{default:a(()=>[o(m,{label:"姓名",prop:"name"},{default:a(()=>[o(p,{modelValue:e.formData.name,"onUpdate:modelValue":t[0]||(t[0]=i=>e.formData.name=i),placeholder:"请输入患者姓名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),o(_,{span:12},{default:a(()=>[o(m,{label:"年龄",prop:"age"},{default:a(()=>[o(v,{modelValue:e.formData.age,"onUpdate:modelValue":t[1]||(t[1]=i=>e.formData.age=i),min:0,max:150,placeholder:"请输入年龄",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(m,{label:"病史",prop:"medical_history"},{default:a(()=>[o(p,{modelValue:e.formData.medical_history,"onUpdate:modelValue":t[2]||(t[2]=i=>e.formData.medical_history=i),type:"textarea",rows:4,placeholder:"请输入患者病史信息",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),o(m,{label:"症状",prop:"symptoms"},{default:a(()=>[o(p,{modelValue:e.formData.symptoms,"onUpdate:modelValue":t[3]||(t[3]=i=>e.formData.symptoms=i),type:"textarea",rows:4,placeholder:"请输入患者症状描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1})),[[R,e.loading]])]),y("div",F,[o(n,null,{header:a(()=>t[7]||(t[7]=[y("span",null,"编辑历史",-1)])),default:a(()=>[o(k,null,{default:a(()=>[o(g,{timestamp:e.formatDate(e.patient.created_at),type:"primary"},{default:a(()=>t[8]||(t[8]=[u(" 创建患者档案 ")])),_:1,__:[8]},8,["timestamp"]),o(g,{timestamp:e.formatDate(e.patient.updated_at),type:"success"},{default:a(()=>t[9]||(t[9]=[u(" 最后更新时间 ")])),_:1,__:[9]},8,["timestamp"]),o(g,{timestamp:e.formatDate(new Date),type:"warning"},{default:a(()=>t[10]||(t[10]=[u(" 当前编辑中... ")])),_:1,__:[10]},8,["timestamp"])]),_:1})]),_:1})])]),_:1},8,["title","breadcrumbs"])}const J=E(L,[["render",G],["__scopeId","data-v-6f111bc2"]]);export{J as default};
