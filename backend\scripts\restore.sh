#!/bin/bash

# 数据库恢复脚本
# 使用方法: ./restore.sh [backup_file] [target_database]

set -e  # 遇到错误立即退出

# 配置变量
DB_HOST=${MYSQL_HOST:-localhost}
DB_PORT=${MYSQL_PORT:-3306}
DB_USER=${MYSQL_USER:-health_user}
DB_PASSWORD=${MYSQL_PASSWORD:-health_password}

BACKUP_FILE="$1"
TARGET_DB="${2:-health_uplink_restore}"
BACKUP_DIR="/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "/log/scripts/restore.log"
}

# 错误处理函数
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 显示使用说明
show_usage() {
    echo "数据库恢复脚本"
    echo "使用方法: $0 [backup_file] [target_database]"
    echo ""
    echo "参数:"
    echo "  backup_file     备份文件路径 (必需)"
    echo "  target_database 目标数据库名 (可选，默认: health_uplink_restore)"
    echo ""
    echo "示例:"
    echo "  $0 /backups/full/health_uplink_full_20231201_120000.sql.gz"
    echo "  $0 /backups/full/health_uplink_full_20231201_120000.sql.gz my_restored_db"
    echo ""
    echo "可用的备份文件:"
    find "$BACKUP_DIR" -name "*.sql.gz" -type f | sort -r | head -10
}

# 检查参数
check_parameters() {
    if [ -z "$BACKUP_FILE" ]; then
        show_usage
        error_exit "请指定备份文件"
    fi
    
    if [ ! -f "$BACKUP_FILE" ]; then
        error_exit "备份文件不存在: $BACKUP_FILE"
    fi
    
    log "备份文件: $BACKUP_FILE"
    log "目标数据库: $TARGET_DB"
}

# 检查MySQL连接
check_mysql_connection() {
    log "检查MySQL连接..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1 || error_exit "无法连接到MySQL数据库"
    log "MySQL连接正常"
}

# 验证备份文件
verify_backup_file() {
    log "验证备份文件..."
    
    # 检查文件是否为空
    if [ ! -s "$BACKUP_FILE" ]; then
        error_exit "备份文件为空: $BACKUP_FILE"
    fi
    
    # 验证gzip文件完整性
    if [[ "$BACKUP_FILE" == *.gz ]]; then
        gzip -t "$BACKUP_FILE" || error_exit "备份文件损坏: $BACKUP_FILE"
        log "压缩文件完整性验证通过"
    fi
    
    # 获取文件信息
    local file_size=$(stat -c%s "$BACKUP_FILE")
    local checksum=$(md5sum "$BACKUP_FILE" | cut -d' ' -f1)
    
    log "文件大小: $(($file_size / 1024 / 1024)) MB"
    log "MD5校验和: $checksum"
}

# 创建目标数据库
create_target_database() {
    log "创建目标数据库: $TARGET_DB"
    
    # 检查数据库是否已存在
    local db_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME='$TARGET_DB';" | grep -c "$TARGET_DB" || true)
    
    if [ "$db_exists" -gt 0 ]; then
        log "警告: 数据库 $TARGET_DB 已存在"
        read -p "是否要删除现有数据库并重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
                -e "DROP DATABASE IF EXISTS $TARGET_DB;" || error_exit "删除现有数据库失败"
            log "已删除现有数据库: $TARGET_DB"
        else
            error_exit "用户取消操作"
        fi
    fi
    
    # 创建新数据库
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "CREATE DATABASE IF NOT EXISTS $TARGET_DB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" || error_exit "创建数据库失败"
    
    log "数据库创建成功: $TARGET_DB"
}

# 恢复数据库
restore_database() {
    log "开始恢复数据库..."
    
    local temp_file="/tmp/restore_${TIMESTAMP}.sql"
    
    # 解压备份文件
    if [[ "$BACKUP_FILE" == *.gz ]]; then
        log "解压备份文件..."
        gunzip -c "$BACKUP_FILE" > "$temp_file" || error_exit "解压备份文件失败"
    else
        cp "$BACKUP_FILE" "$temp_file"
    fi
    
    # 检查SQL文件内容
    local sql_size=$(stat -c%s "$temp_file")
    if [ "$sql_size" -eq 0 ]; then
        rm -f "$temp_file"
        error_exit "解压后的SQL文件为空"
    fi
    
    log "SQL文件大小: $(($sql_size / 1024 / 1024)) MB"
    
    # 执行恢复
    log "执行数据库恢复..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$TARGET_DB" < "$temp_file" || {
        rm -f "$temp_file"
        error_exit "数据库恢复失败"
    }
    
    # 清理临时文件
    rm -f "$temp_file"
    
    log "数据库恢复完成"
}

# 验证恢复结果
verify_restore() {
    log "验证恢复结果..."
    
    # 检查表数量
    local table_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='$TARGET_DB';" | tail -n 1)
    
    log "恢复的表数量: $table_count"
    
    if [ "$table_count" -eq 0 ]; then
        error_exit "恢复验证失败: 没有找到任何表"
    fi
    
    # 显示表列表
    log "恢复的表列表:"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "SHOW TABLES;" "$TARGET_DB" | tail -n +2 | while read table; do
        local row_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
            -e "SELECT COUNT(*) FROM $table;" "$TARGET_DB" | tail -n 1)
        log "  $table: $row_count 行"
    done
    
    log "恢复验证完成"
}

# 生成恢复报告
generate_restore_report() {
    local report_file="$BACKUP_DIR/logs/restore_report_${TIMESTAMP}.txt"
    
    {
        echo "数据库恢复报告"
        echo "================"
        echo "恢复时间: $(date)"
        echo "备份文件: $BACKUP_FILE"
        echo "目标数据库: $TARGET_DB"
        echo "MySQL主机: $DB_HOST:$DB_PORT"
        echo ""
        echo "恢复统计:"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
            -e "SELECT TABLE_NAME, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='$TARGET_DB' ORDER BY TABLE_NAME;" | column -t
    } > "$report_file"
    
    log "恢复报告已生成: $report_file"
}

# 主函数
main() {
    log "开始数据库恢复任务"
    
    # 检查参数
    check_parameters
    
    # 检查MySQL连接
    check_mysql_connection
    
    # 验证备份文件
    verify_backup_file
    
    # 创建目标数据库
    create_target_database
    
    # 恢复数据库
    restore_database
    
    # 验证恢复结果
    verify_restore
    
    # 生成恢复报告
    generate_restore_report
    
    log "数据库恢复任务完成"
    log "目标数据库: $TARGET_DB"
}

# 执行主函数
main "$@"
