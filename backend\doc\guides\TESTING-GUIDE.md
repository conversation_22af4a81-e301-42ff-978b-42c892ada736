# Health Uplink 功能测试指南

## 🎯 测试目标

验证患者管理的详情、编辑、删除功能是否正常工作。

## 🌐 测试环境

- **前端地址**: http://localhost:5173/
- **后端地址**: http://localhost:3000/
- **测试浏览器**: Chrome/Edge (推荐)

## 📋 功能测试清单

### 1. 患者列表功能测试

**访问地址**: http://localhost:5173/patients

#### ✅ 测试步骤：
1. 打开患者列表页面
2. 检查页面是否正常加载
3. 检查是否显示患者数据
4. 检查搜索功能是否可用
5. 检查分页功能是否正常

#### 🔍 预期结果：
- 页面正常显示
- 显示患者列表（至少1个患者）
- 每个患者有"详情"、"编辑"、"删除"按钮

### 2. 患者详情功能测试

#### ✅ 测试步骤：
1. 在患者列表中点击任意患者的"详情"按钮
2. 检查详情页面是否正常加载
3. 检查患者信息是否完整显示
4. 检查"编辑"和"删除"按钮是否存在

#### 🔍 预期结果：
- 跳转到详情页面 (URL: `/patient/[id]`)
- 显示完整的患者信息
- 页面顶部有"编辑"和"删除"按钮
- 面包屑导航正常显示

### 3. 患者编辑功能测试

#### ✅ 测试步骤：
1. 在患者详情页面点击"编辑"按钮
2. 检查编辑页面是否正常加载
3. 检查表单是否预填充了患者数据
4. 修改患者信息（如姓名、年龄等）
5. 点击"保存"按钮
6. 检查是否成功保存并跳转回详情页

#### 🔍 预期结果：
- 跳转到编辑页面 (URL: `/patient/[id]/edit`)
- 表单预填充现有数据
- 表单验证正常工作
- 保存成功后显示成功提示
- 自动跳转回患者详情页面

### 4. 患者删除功能测试

#### ✅ 测试步骤：
1. 在患者详情页面点击"删除"按钮
2. 检查是否弹出确认对话框
3. 点击"确定"确认删除
4. 检查是否显示删除成功提示
5. 检查是否跳转回患者列表页面

#### 🔍 预期结果：
- 弹出确认删除对话框
- 确认后显示删除成功提示
- 自动跳转回患者列表页面
- 患者从列表中消失（如果有数据库）

## 🔧 开发者工具测试

### 使用浏览器开发者工具监控API调用

1. **打开开发者工具**：
   - 按 F12 或右键选择"检查"
   - 切换到 "Network" 标签页

2. **监控API请求**：
   - 执行上述功能测试
   - 在Network标签中查看API请求
   - 检查请求状态码和响应数据

3. **预期的API调用**：
   ```
   GET /api/patient/list - 获取患者列表
   GET /api/patient/[id] - 获取患者详情
   PUT /api/patient/[id] - 更新患者信息
   DELETE /api/patient/[id] - 删除患者信息
   ```

## 🐛 常见问题排查

### 问题1: 页面无法加载
**解决方案**：
- 检查前端服务是否启动 (http://localhost:5173)
- 检查后端服务是否启动 (http://localhost:3000)
- 检查浏览器控制台是否有错误信息

### 问题2: API调用失败
**解决方案**：
- 检查Network标签中的API请求状态
- 检查是否有认证错误 (401)
- 检查后端日志是否有错误信息

### 问题3: 功能按钮无响应
**解决方案**：
- 检查浏览器控制台是否有JavaScript错误
- 刷新页面重试
- 检查是否有网络连接问题

## 📊 测试报告模板

### 测试结果记录

| 功能 | 测试状态 | 备注 |
|------|----------|------|
| 患者列表显示 | ✅/❌ | |
| 患者详情查看 | ✅/❌ | |
| 患者信息编辑 | ✅/❌ | |
| 患者信息删除 | ✅/❌ | |
| 页面导航 | ✅/❌ | |
| 错误处理 | ✅/❌ | |

### 发现的问题

1. **问题描述**: 
   - **重现步骤**: 
   - **预期结果**: 
   - **实际结果**: 
   - **严重程度**: 高/中/低

## 🚀 快速测试命令

### 启动服务
```bash
# 启动后端服务
cd backend
node app.js

# 启动前端服务
cd frontend
npm run dev
```

### 快速访问链接
- **患者列表**: http://localhost:5173/patients
- **系统状态**: http://localhost:5173/status
- **组件展示**: http://localhost:5173/components

## 🎉 测试完成标准

当以下所有项目都通过测试时，功能验证完成：

- ✅ 患者列表正常显示
- ✅ 患者详情正常查看
- ✅ 患者信息可以编辑和保存
- ✅ 患者信息可以删除
- ✅ 页面导航正常工作
- ✅ 错误处理机制正常
- ✅ API调用状态正常
- ✅ 用户体验流畅

---

**注意**: 如果在测试过程中发现任何问题，请记录详细的错误信息和重现步骤，以便进行进一步的调试和修复。
