import{_ as y,d as x,c as f,o as S,w as n,b as e,a as l,r as t,e as r,v as k,x as w,D as F,S as _,C as D,f as i,q as C}from"./index-D1pIs7PZ.js";const P=x({name:"ComponentDemo",components:{ContentContainer:D,SearchForm:_,DataTable:F,StandardPagination:w,StandardForm:k},setup(){const a=i([{key:"name",label:"姓名",type:"input",placeholder:"请输入姓名"},{key:"status",label:"状态",type:"select",placeholder:"请选择状态",options:[{label:"启用",value:1},{label:"禁用",value:0}]},{key:"date_range",label:"日期范围",type:"daterange",placeholder:"选择日期范围"}]),s=i([{id:1,name:"张三",age:25,status:1,email:"z<PERSON><PERSON>@example.com",created_at:"2024-01-01"},{id:2,name:"李四",age:30,status:0,email:"<EMAIL>",created_at:"2024-01-02"},{id:3,name:"王五",age:28,status:1,email:"<EMAIL>",created_at:"2024-01-03"}]),c=i([{prop:"id",label:"ID",width:80,align:"center"},{prop:"name",label:"姓名",minWidth:100},{prop:"age",label:"年龄",width:80,align:"center"},{prop:"email",label:"邮箱",minWidth:150},{prop:"status",label:"状态",width:100,type:"status",statusMap:{1:{text:"启用",type:"success"},0:{text:"禁用",type:"danger"}}},{prop:"created_at",label:"创建时间",width:120}]),m=i({page:1,pageSize:10,total:100}),p=i([{prop:"name",label:"姓名",type:"input",placeholder:"请输入姓名",required:!0},{prop:"age",label:"年龄",type:"number",min:0,max:150},{prop:"gender",label:"性别",type:"radio",options:[{label:"男",value:"male"},{label:"女",value:"female"}]},{prop:"hobbies",label:"爱好",type:"checkbox",options:[{label:"读书",value:"reading"},{label:"运动",value:"sports"},{label:"音乐",value:"music"}]},{prop:"description",label:"描述",type:"textarea",rows:3,placeholder:"请输入描述信息"}]),u=C({name:"",age:null,gender:"",hobbies:[],description:""});return{demoSearchFields:a,demoTableData:s,demoTableColumns:c,demoPagination:m,demoFormFields:p,demoFormModel:u,handleDemoSearch:o=>{console.log("搜索数据:",o)},handleDemoReset:()=>{console.log("重置搜索")},handleRefresh:()=>{console.log("刷新数据")},handleFormSubmit:o=>{console.log("表单提交:",o)}}}}),R={class:"demo-section"},T={class:"subsection"},z={class:"subsection"},M={class:"subsection"},$={class:"subsection"};function B(a,s,c,m,p,u){const b=t("SearchForm"),d=t("el-button"),v=t("DataTable"),g=t("StandardPagination"),o=t("StandardForm"),h=t("ContentContainer");return S(),f(h,{title:"UI 组件展示",description:"Health Uplink 标准组件库展示和使用示例","show-header":!0},{default:n(()=>[s[8]||(s[8]=e("div",{class:"demo-section"},[e("h3",{class:"section-title"},"设计系统"),e("div",{class:"subsection"},[e("h4",{class:"subsection-title"},"颜色系统"),e("div",{class:"color-palette"},[e("div",{class:"color-group"},[e("h5",null,"主色调"),e("div",{class:"color-items"},[e("div",{class:"color-item"},[e("div",{class:"color-block",style:{background:"var(--primary-color)"}}),e("span",null,"Primary")]),e("div",{class:"color-item"},[e("div",{class:"color-block",style:{background:"var(--primary-light)"}}),e("span",null,"Primary Light")]),e("div",{class:"color-item"},[e("div",{class:"color-block",style:{background:"var(--primary-dark)"}}),e("span",null,"Primary Dark")])])]),e("div",{class:"color-group"},[e("h5",null,"辅助色"),e("div",{class:"color-items"},[e("div",{class:"color-item"},[e("div",{class:"color-block",style:{background:"var(--success-color)"}}),e("span",null,"Success")]),e("div",{class:"color-item"},[e("div",{class:"color-block",style:{background:"var(--warning-color)"}}),e("span",null,"Warning")]),e("div",{class:"color-item"},[e("div",{class:"color-block",style:{background:"var(--error-color)"}}),e("span",null,"Error")])])])])]),e("div",{class:"subsection"},[e("h4",{class:"subsection-title"},"字体系统"),e("div",{class:"typography-demo"},[e("div",{class:"text-xxl"},"主标题 (24px)"),e("div",{class:"text-xl"},"标题 (20px)"),e("div",{class:"text-lg"},"小标题 (18px)"),e("div",{class:"text-md"},"正文 (16px)"),e("div",{class:"text-sm"},"正文（小）(14px)"),e("div",{class:"text-xs"},"辅助文字 (12px)")])]),e("div",{class:"subsection"},[e("h4",{class:"subsection-title"},"间距系统"),e("div",{class:"spacing-demo"},[e("div",{class:"spacing-item"},[e("div",{class:"spacing-block",style:{width:"4px"}}),e("span",null,"XS (4px)")]),e("div",{class:"spacing-item"},[e("div",{class:"spacing-block",style:{width:"8px"}}),e("span",null,"SM (8px)")]),e("div",{class:"spacing-item"},[e("div",{class:"spacing-block",style:{width:"16px"}}),e("span",null,"MD (16px)")]),e("div",{class:"spacing-item"},[e("div",{class:"spacing-block",style:{width:"24px"}}),e("span",null,"LG (24px)")]),e("div",{class:"spacing-item"},[e("div",{class:"spacing-block",style:{width:"32px"}}),e("span",null,"XL (32px)")])])])],-1)),e("div",R,[s[7]||(s[7]=e("h3",{class:"section-title"},"标准组件",-1)),e("div",T,[s[0]||(s[0]=e("h4",{class:"subsection-title"},"SearchForm - 搜索表单",-1)),l(b,{"search-fields":a.demoSearchFields,onSearch:a.handleDemoSearch,onReset:a.handleDemoReset},null,8,["search-fields","onSearch","onReset"])]),e("div",z,[s[4]||(s[4]=e("h4",{class:"subsection-title"},"DataTable - 数据表格",-1)),l(v,{data:a.demoTableData,columns:a.demoTableColumns,pagination:a.demoPagination,title:"示例数据表格",onRefresh:a.handleRefresh},{actions:n(({row:I})=>[l(d,{type:"text",size:"small"},{default:n(()=>s[1]||(s[1]=[r("查看")])),_:1,__:[1]}),l(d,{type:"text",size:"small"},{default:n(()=>s[2]||(s[2]=[r("编辑")])),_:1,__:[2]}),l(d,{type:"text",size:"small",class:"text-error"},{default:n(()=>s[3]||(s[3]=[r("删除")])),_:1,__:[3]})]),_:1},8,["data","columns","pagination","onRefresh"])]),e("div",M,[s[5]||(s[5]=e("h4",{class:"subsection-title"},"StandardPagination - 分页组件",-1)),l(g,{"current-page":a.demoPagination.page,"page-size":a.demoPagination.pageSize,total:a.demoPagination.total,"show-info":!0},null,8,["current-page","page-size","total"])]),e("div",$,[s[6]||(s[6]=e("h4",{class:"subsection-title"},"StandardForm - 标准表单",-1)),l(o,{"form-fields":a.demoFormFields,"form-model":a.demoFormModel,"show-actions":!0,onSubmit:a.handleFormSubmit},null,8,["form-fields","form-model","onSubmit"])])]),s[9]||(s[9]=e("div",{class:"demo-section"},[e("h3",{class:"section-title"},"工具类展示"),e("div",{class:"subsection"},[e("h4",{class:"subsection-title"},"文字样式"),e("div",{class:"utility-demo"},[e("div",{class:"text-primary font-bold"},"主要文字 - 粗体"),e("div",{class:"text-secondary font-medium"},"次要文字 - 中等"),e("div",{class:"text-tertiary font-normal"},"第三级文字 - 正常"),e("div",{class:"text-success"},"成功状态文字"),e("div",{class:"text-warning"},"警告状态文字"),e("div",{class:"text-error"},"错误状态文字")])]),e("div",{class:"subsection"},[e("h4",{class:"subsection-title"},"布局工具"),e("div",{class:"utility-demo"},[e("div",{class:"flex items-center justify-between p-md bg-tertiary rounded-md mb-sm"},[e("span",null,"左侧内容"),e("span",null,"右侧内容")]),e("div",{class:"flex flex-col gap-sm"},[e("div",{class:"p-sm bg-primary rounded-sm shadow-sm"},"卡片 1"),e("div",{class:"p-sm bg-primary rounded-sm shadow-sm"},"卡片 2"),e("div",{class:"p-sm bg-primary rounded-sm shadow-sm"},"卡片 3")])])])],-1))]),_:1,__:[8,9]})}const N=y(P,[["render",B],["__scopeId","data-v-987b58a6"]]);export{N as default};
