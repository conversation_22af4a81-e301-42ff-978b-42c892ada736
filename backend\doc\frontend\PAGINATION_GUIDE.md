# 分页配置统一管理指南

## 📋 概述

为了解决不同页面分页配置不一致的问题，我们建立了统一的分页配置管理系统。所有页面的分页设置都在 `src/config/pagination.js` 中集中管理。

## 🎯 解决的问题

### 问题现象
- **患者页面**：每页显示 10 条记录，分页选项 [10, 20, 50, 100]
- **图片管理页面**：每页显示 24 条记录，分页选项 [12, 24, 48, 96]
- **其他页面**：各自使用不同的分页配置

### 问题根源
1. 各页面独立设置分页配置
2. 没有统一的分页标准
3. 组件默认值不一致

## 🔧 解决方案

### 1. 统一配置文件
创建 `src/config/pagination.js` 文件，定义三种分页类型：

```javascript
// 表格类页面（患者列表、医生列表等）
TABLE: {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZES: [10, 20, 50, 100]
}

// 网格类页面（图片管理、文件管理等）
GRID: {
  DEFAULT_PAGE_SIZE: 24,
  PAGE_SIZES: [12, 24, 48, 96]
}

// 小组件（下拉选择器、弹窗列表等）
WIDGET: {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZES: [5, 10, 20, 50]
}
```

### 2. 页面特定配置
为每个页面定义专门的分页配置：

```javascript
export const PAGE_PAGINATION_CONFIG = {
  PATIENT_LIST: {
    type: 'TABLE',
    pageSize: 20,
    pageSizes: [10, 20, 50, 100]
  },
  
  IMAGE_LIST: {
    type: 'GRID',
    pageSize: 24,
    pageSizes: [12, 24, 48, 96]
  }
};
```

### 3. 统一使用方式
所有页面都使用统一的配置函数：

```javascript
import { getPagePaginationConfig } from '../config/pagination';

// 在组件中使用
const pagination = ref(getPagePaginationConfig('PATIENT_LIST'));
```

## 📊 当前分页配置

### 表格类页面
- **默认每页**：20 条
- **选项**：[10, 20, 50, 100]
- **适用页面**：
  - 患者列表 (PATIENT_LIST)
  - 医生列表 (DOCTOR_LIST)
  - 病历记录 (MEDICAL_RECORD_LIST)
  - 症状记录 (SYMPTOM_RECORD_LIST)

### 网格类页面
- **默认每页**：24 条
- **选项**：[12, 24, 48, 96]
- **适用页面**：
  - 图片管理 (IMAGE_LIST)
  - 文件管理等

### 小组件
- **默认每页**：10 条
- **选项**：[5, 10, 20, 50]
- **适用场景**：
  - 下拉选择器
  - 弹窗列表
  - 侧边栏列表

## 🚀 使用指南

### 1. 新页面开发
```javascript
// 导入配置函数
import { getPagePaginationConfig } from '../config/pagination';

export default {
  setup() {
    // 使用页面特定配置
    const pagination = ref(getPagePaginationConfig('YOUR_PAGE_NAME'));
    
    return { pagination };
  }
}
```

### 2. 添加新页面配置
在 `src/config/pagination.js` 中添加新页面配置：

```javascript
export const PAGE_PAGINATION_CONFIG = {
  // 现有配置...
  
  // 新页面配置
  YOUR_PAGE_NAME: {
    type: 'TABLE', // 或 'GRID', 'WIDGET'
    pageSize: 20,
    pageSizes: [10, 20, 50, 100]
  }
};
```

### 3. 修改现有配置
直接在配置文件中修改，所有使用该配置的页面会自动更新：

```javascript
PATIENT_LIST: {
  type: 'TABLE',
  pageSize: 25, // 修改默认每页数量
  pageSizes: [10, 25, 50, 100] // 修改选项
}
```

## 🎨 组件集成

### StandardPagination 组件
已集成统一配置，自动使用正确的分页选项。

### DataTable 组件
内置 StandardPagination，支持表格类分页配置。

### ImageManager 组件
内置 StandardPagination，支持网格类分页配置。

## 📝 最佳实践

### 1. 选择合适的分页类型
- **表格数据**：使用 TABLE 类型，适合文本数据
- **图片/卡片**：使用 GRID 类型，适合视觉内容
- **选择器**：使用 WIDGET 类型，适合小列表

### 2. 合理设置每页数量
- **表格**：10-50 条，平衡加载速度和浏览体验
- **网格**：12-48 条，考虑屏幕尺寸和网格布局
- **小组件**：5-20 条，避免滚动过多

### 3. 保持一致性
- 同类型页面使用相同的分页配置
- 避免在页面中硬编码分页参数
- 统一使用配置文件管理

## 🔍 调试和验证

### 检查当前配置
```javascript
import { getPagePaginationConfig } from '../config/pagination';

// 查看页面配置
console.log('患者列表分页配置:', getPagePaginationConfig('PATIENT_LIST'));
console.log('图片管理分页配置:', getPagePaginationConfig('IMAGE_LIST'));
```

### 验证分页行为
1. 检查默认每页数量是否正确
2. 验证分页选项是否符合预期
3. 确认页面切换时分页状态正确

## 🎯 预期效果

实施统一分页配置后：

1. **患者列表页面**：每页 20 条，选项 [10, 20, 50, 100]
2. **图片管理页面**：每页 24 条，选项 [12, 24, 48, 96]
3. **所有新页面**：自动使用合适的分页配置
4. **维护性**：集中管理，易于调整

## 📞 技术支持

如需修改分页配置或添加新页面配置，请：
1. 修改 `src/config/pagination.js` 文件
2. 更新相关文档
3. 测试相关页面功能
4. 通知团队成员配置变更
