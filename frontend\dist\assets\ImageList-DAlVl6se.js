import{_ as R,d as $,c as U,o as z,w as i,a as c,r as g,e as m,f as u,g as v,h as M,s as y,E as t}from"./index-D1pIs7PZ.js";import{g as F}from"./pagination-DE-V6uRf.js";const j=$({name:"ImageList",setup(){const e=u([]),s=u(!1),d=u({}),n=u(F("IMAGE_LIST")),f=v(()=>[{title:"首页",path:"/"},{title:"图片管理",path:"/images"}]),b=v(()=>[{key:"patient_id",label:"患者ID",type:"input",placeholder:"请输入患者ID",clearable:!0},{key:"filename",label:"文件名",type:"input",placeholder:"请输入文件名",clearable:!0},{key:"date_range",label:"上传时间",type:"daterange",placeholder:"选择时间范围",clearable:!0}]),l=async()=>{s.value=!0;try{const a={page:n.value.page,limit:n.value.pageSize,...d.value},o=await y.get("/image/list",{params:a});o.images?(e.value=o.images,n.value.total=o.total,console.log("成功加载图片:",e.value.length,"张")):(e.value=[],n.value.total=0,t.error(o.message||"获取图片列表失败"))}catch(a){console.error("获取图片列表失败:",a);let o="获取图片列表失败";a.message.includes("权限不足")&&(o="权限不足，请重新登录"),t.error(o),e.value=[],n.value.total=0}finally{s.value=!1}},r=a=>{d.value=a,n.value.page=1,l()},p=()=>{d.value={},n.value.page=1,l()},h=a=>{n.value.pageSize=a,n.value.page=1,l()},S=a=>{n.value.page=a,l()},w=async a=>{try{const o=await y.delete(`/image/${a.image_id}`);o.success?(t.success("图片删除成功"),l()):t.error(o.message||"删除图片失败")}catch(o){console.error("删除图片失败:",o),t.error("删除图片失败，请稍后重试")}},_=a=>{console.log("下载图片:",a.original_name),t.success(`开始下载: ${a.original_name}`)},I=()=>{t.info("上传功能开发中...")},k=async()=>{try{const C=await(await fetch("http://your-production-api-url.com/api/debug/images")).json();console.log("图片服务调试信息:",C),t.success(`图片服务正常！找到 ${C.count} 个图片文件`)}catch(a){console.error("图片服务测试失败:",a),t.error(`图片服务测试失败: ${a.message}`)}},D=()=>{console.log("检查滚动状态"),t.info("滚动检查完成")};return M(()=>{l()}),{images:e,loading:s,pagination:n,breadcrumbs:f,searchFields:b,fetchImages:l,handleSearch:r,handleReset:p,handleSizeChange:h,handleCurrentChange:S,handleDelete:w,handleDownload:_,handleUpload:I,testImageService:k,adjustGridScrolling:D}}});function E(e,s,d,n,f,b){const l=g("SearchForm"),r=g("el-button"),p=g("ImageManager"),h=g("ContentContainer");return z(),U(h,{title:"图片管理",description:"管理和查看患者上传的医疗影像资料","show-header":!0,breadcrumbs:e.breadcrumbs,"show-breadcrumb":!0},{default:i(()=>[c(l,{"search-fields":e.searchFields,loading:e.loading,onSearch:e.handleSearch,onReset:e.handleReset},null,8,["search-fields","loading","onSearch","onReset"]),c(p,{images:e.images,loading:e.loading,pagination:e.pagination,"show-debug-info":!0,onUpload:e.handleUpload,onDelete:e.handleDelete,onDownload:e.handleDownload,onCurrentChange:e.handleCurrentChange,onSizeChange:e.handleSizeChange,onRefresh:e.fetchImages},{"toolbar-actions":i(()=>[c(r,{type:"primary",onClick:e.handleUpload,icon:"Upload"},{default:i(()=>s[0]||(s[0]=[m(" 上传图片 ")])),_:1,__:[0]},8,["onClick"]),c(r,{type:"info",onClick:e.testImageService,icon:"Connection"},{default:i(()=>s[1]||(s[1]=[m(" 测试图片服务 ")])),_:1,__:[1]},8,["onClick"]),c(r,{type:"warning",onClick:e.adjustGridScrolling,icon:"Refresh"},{default:i(()=>s[2]||(s[2]=[m(" 检查滚动 ")])),_:1,__:[2]},8,["onClick"])]),_:1},8,["images","loading","pagination","onUpload","onDelete","onDownload","onCurrentChange","onSizeChange","onRefresh"])]),_:1},8,["breadcrumbs"])}const P=R(j,[["render",E],["__scopeId","data-v-2879fcb9"]]);export{P as default};
