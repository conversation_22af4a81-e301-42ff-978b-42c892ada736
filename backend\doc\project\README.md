# Health Uplink - 健康信息收集系统

一个基于微信小程序的健康信息收集系统，包含患者信息录入、医疗影像上传和Web管理后台。

## 📋 项目概述

Health Uplink 是一个完整的医疗健康信息收集解决方案，主要用于医院、诊所等医疗机构收集和管理患者信息。

### 主要功能

- 🏥 **患者信息收集**: 通过微信小程序收集患者基本信息、病史和症状
- 📸 **医疗影像上传**: 支持检查单、患处照片等医疗影像的上传
- 💻 **Web管理后台**: 提供数据管理、导出和统计功能
- 🔐 **安全认证**: JWT身份认证和API访问控制
- 📊 **数据导出**: 支持CSV格式的数据导出

## 🏗️ 技术架构

### 前端技术栈
- **微信小程序**: 原生开发，使用Skyline渲染引擎
- **管理后台**: Vue 3 + Element Plus + Vite

### 后端技术栈
- **运行环境**: Node.js
- **Web框架**: Express.js 5.1.0
- **数据库**: MySQL
- **ORM**: Sequelize 6.37.7
- **文件上传**: Multer
- **身份认证**: JWT + bcryptjs
- **日志记录**: Winston

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- 微信开发者工具

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd health-uplink
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **安装管理后台依赖**
```bash
cd ../frontend
npm install
```

4. **配置数据库**
```bash
# 在backend目录下创建.env文件
cp .env.example .env
# 编辑.env文件，配置数据库连接信息
```

5. **启动服务**
```bash
# 使用启动脚本（Windows）
start_admin.bat

# 或手动启动
# 启动后端服务
cd backend && node app.js

# 启动管理后台
cd ../frontend && npm run dev
```

### 环境配置

在 `backend/.env` 文件中配置以下环境变量：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=health_uplink
DB_USER=your_username
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret_key

# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库同步配置（开发环境）
DB_SYNC_FORCE=false
```

## 📱 微信小程序配置

1. 在微信开发者工具中导入项目
2. 修改 `utils/api.js` 中的 `baseUrl` 为你的后端服务地址
3. 在微信公众平台配置服务器域名

## 🔧 API 文档

### 患者信息接口

#### 提交患者信息
```http
POST /api/patient/info
Content-Type: application/json

{
  "name": "张三",
  "age": 30,
  "medical_history": "高血压病史",
  "symptoms": "头痛、头晕"
}
```

#### 获取患者列表
```http
GET /api/patient/list?page=1&limit=10
Authorization: Bearer <token>
```

### 图片上传接口

#### 上传图片
```http
POST /api/image/upload
Content-Type: multipart/form-data

images: <file>
```

### 用户认证接口

#### 管理员登录
```http
POST /api/user/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

## 📊 数据库结构

### patients 表
| 字段 | 类型 | 说明 |
|------|------|------|
| patient_id | INT | 主键，自增 |
| name | VARCHAR(100) | 患者姓名 |
| age | INT | 年龄 |
| medical_history | TEXT | 病史 |
| symptoms | TEXT | 症状 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### images 表
| 字段 | 类型 | 说明 |
|------|------|------|
| image_id | INT | 主键，自增 |
| filename | VARCHAR(255) | 文件名 |
| original_name | VARCHAR(255) | 原始文件名 |
| file_path | VARCHAR(500) | 文件路径 |
| file_size | INT | 文件大小 |
| created_at | DATETIME | 上传时间 |

## 🔒 安全特性

- **JWT身份认证**: 管理后台使用JWT进行身份验证
- **密码加密**: 使用bcryptjs对密码进行哈希加密
- **API限流**: 15分钟内每个IP最多100次请求
- **CORS配置**: 跨域资源共享配置
- **文件上传限制**: 限制上传文件类型和大小

## 📊 监控和告警

### APM监控功能

- **性能指标**: HTTP请求响应时间、吞吐量、错误率
- **系统监控**: CPU、内存、数据库连接状态
- **健康检查**: 自动健康检查和状态报告
- **Prometheus集成**: 支持Prometheus指标收集

### 监控端点

```bash
# 健康检查
GET /health

# 详细健康检查
GET /monitoring/health/detailed

# Prometheus指标
GET /monitoring/metrics

# 系统状态
GET /monitoring/status

# 应用信息
GET /monitoring/info
```

## 💾 数据备份策略

### 自动备份

- **完整备份**: 每天凌晨2点自动执行
- **增量备份**: 每4小时执行一次
- **备份保留**: 默认保留30天的备份文件
- **备份验证**: 自动验证备份文件完整性

### 备份管理API

```bash
# 获取备份列表
GET /api/backup/list

# 创建备份
POST /api/backup/create
{
  "type": "full" // 或 "incremental"
}

# 恢复数据库
POST /api/backup/restore
{
  "filename": "backup_file.sql.gz",
  "targetDatabase": "restored_db"
}

# 下载备份文件
GET /api/backup/download/:filename
```

### 手动备份

```bash
# 执行完整备份
./scripts/backup.sh health_uplink full

# 执行增量备份
./scripts/backup.sh health_uplink incremental

# 恢复数据库
./scripts/restore.sh /backups/full/backup_file.sql.gz target_database
```

## 📝 开发指南

### 项目结构
```
health-uplink/
├── pages/              # 小程序页面
├── components/         # 小程序组件
├── utils/             # 工具函数
├── backend/           # 后端服务
│   ├── controllers/   # 控制器
│   ├── models/        # 数据模型
│   ├── routes/        # 路由
│   ├── services/      # 业务逻辑
│   ├── middlewares/   # 中间件
│   └── frontend/ # 管理后台
└── images/            # 静态资源
```

### 代码规范

- 使用ES6+语法
- 遵循RESTful API设计原则
- 统一错误处理和日志记录
- 代码注释清晰完整

## 🚀 部署指南

### Docker部署（推荐）

1. **使用Docker Compose一键部署**
```bash
# 克隆项目
git clone <repository-url>
cd health-uplink

# 启动所有服务
chmod +x docker-start.sh
./docker-start.sh start
```

2. **Docker命令说明**
```bash
./docker-start.sh start    # 启动所有服务
./docker-start.sh stop     # 停止所有服务
./docker-start.sh restart  # 重启服务
./docker-start.sh status   # 查看服务状态
./docker-start.sh logs     # 查看日志
./docker-start.sh cleanup  # 清理所有数据
```

3. **服务访问地址**
- 管理后台: http://localhost
- 后端API: http://localhost:3000
- 健康检查: http://localhost:3000/health
- 监控指标: http://localhost:3000/monitoring/metrics

### 传统部署

1. **环境配置**
```bash
NODE_ENV=production
DB_SYNC_FORCE=false
```

2. **构建管理后台**
```bash
cd ../frontend
npm run build
```

3. **启动服务**
```bash
cd backend
npm start
```

### 使用PM2部署
```bash
npm install -g pm2
pm2 start app.js --name health-uplink
pm2 startup
pm2 save
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-repo/health-uplink/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。