# 数据库配置 - 本地开发环境
DB_HOST=localhost
DB_PORT=3306
DB_NAME=health_uplink
DB_USER=health_user
DB_PASSWORD=health_user

# JWT配置
JWT_SECRET=local_secret_key_for_development

# 服务器配置
PORT=3000
NODE_ENV=development
API_BASE_URL=http://localhost:3000

# 数据库同步配置 (开发时建议开启)
DB_SYNC_ENABLED=true
DB_SYNC_FORCE=false
DB_SYNC_ALTER=true # 使用 alter 模式方便开发中模型变更

# 文件上传配置
UPLOAD_DIR=uploads/images
MAX_FILE_SIZE=10485760  # 10MB

# 日志配置
LOG_LEVEL=info
LOG_DIR=log/backend

# 管理员默认账户
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123

# API限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000 # 本地开发放宽限制