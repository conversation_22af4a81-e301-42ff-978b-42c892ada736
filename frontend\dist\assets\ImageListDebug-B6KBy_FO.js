import{_ as h,d as I,m as o,o as i,b as t,t as a,l as p,F as k,n as y,f as d,s as b}from"./index-D1pIs7PZ.js";const c=I({name:"ImageListDebug",setup(){const e=d(!1),s=d([]),r=d(null),u=d(null),m=d(localStorage.getItem("token")),g=d("http://your-production-api-url.com");return{loading:e,images:s,apiResult:r,apiError:u,token:m,apiBaseUrl:g,testApi:async()=>{e.value=!0,r.value=null,u.value=null;try{const f=await(await fetch("http://localhost:3000/health")).json();r.value=f}catch(l){u.value=l.message}finally{e.value=!1}},fetchImages:async()=>{e.value=!0,r.value=null,u.value=null;try{const l=await b.get("/image/list");s.value=l.images||[],r.value=l}catch(l){u.value=l.message,console.error("获取图片列表失败:",l)}finally{e.value=!1}},getImageUrl:l=>l?l.startsWith("http")?l:`${g.value}${l}`:""}}}),A={class:"debug-container"},U={class:"debug-section"},B={class:"debug-section"},L=["disabled"],R={key:0,class:"api-result"},$={key:1,class:"api-error"},P={class:"debug-section"},C=["disabled"],E={key:0},w={key:1},D={key:2};function N(e,s,r,u,m,g){return i(),o("div",A,[s[8]||(s[8]=t("h2",null,"图片管理调试页面",-1)),t("div",U,[s[2]||(s[2]=t("h3",null,"环境信息",-1)),t("p",null,"API Base URL: "+a(e.apiBaseUrl),1),t("p",null,"Token: "+a(e.token?e.token.substring(0,20)+"...":"无"),1)]),t("div",B,[s[5]||(s[5]=t("h3",null,"API测试",-1)),t("button",{onClick:s[0]||(s[0]=(...n)=>e.testApi&&e.testApi(...n)),disabled:e.loading},"测试API连接",8,L),e.apiResult?(i(),o("div",R,[s[3]||(s[3]=t("h4",null,"API响应:",-1)),t("pre",null,a(JSON.stringify(e.apiResult,null,2)),1)])):p("",!0),e.apiError?(i(),o("div",$,[s[4]||(s[4]=t("h4",null,"API错误:",-1)),t("pre",null,a(e.apiError),1)])):p("",!0)]),t("div",P,[s[7]||(s[7]=t("h3",null,"图片列表",-1)),t("button",{onClick:s[1]||(s[1]=(...n)=>e.fetchImages&&e.fetchImages(...n)),disabled:e.loading},"获取图片列表",8,C),e.loading?(i(),o("div",E,"加载中...")):p("",!0),e.images.length>0?(i(),o("div",w,[t("p",null,"找到 "+a(e.images.length)+" 张图片",1),(i(!0),o(k,null,y(e.images,(n,v)=>(i(),o("div",{key:n.image_id,class:"image-item"},[t("p",null,"图片 "+a(v+1)+": "+a(n.original_name),1),t("p",null,"URL: "+a(n.image_url),1),t("p",null,"完整URL: "+a(e.getImageUrl(n.image_url)),1)]))),128))])):e.loading?p("",!0):(i(),o("div",D,s[6]||(s[6]=[t("p",null,"没有找到图片",-1)])))])])}const V=h(c,[["render",N],["__scopeId","data-v-24ec080a"]]);export{V as default};
