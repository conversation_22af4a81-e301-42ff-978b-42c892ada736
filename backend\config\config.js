const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config(); // 加载 .env 文件中的环境变量

const sequelize = new Sequelize(
  process.env.DB_NAME || 'health_db',
  process.env.DB_USER || 'root',
  process.env.DB_PASSWORD || 'zn123',
  {
    host: process.env.DB_HOST || '************',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: false, // 设置为 true 可以看到 SQL 查询日志
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// 测试数据库连接
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功！');
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
}

module.exports = {
  sequelize,
  testConnection
};