# Health Uplink 完整开发计划

## 项目概述
Health Uplink 是一个健康信息收集系统，包含：
- 微信小程序端：患者信息收集和图片上传
- 后端管理系统：数据管理和导出
- 管理前端：数据查看和统计

---

## 第一部分：微信小程序开发

### 阶段一：项目初始化和基础配置

#### 1. 创建项目基础结构
- [ ] 创建项目根目录 `health-uplink`
- [ ] 初始化微信小程序项目结构
- [ ] 创建 `app.js`、`app.json`、`app.wxss` 主要配置文件
- [ ] 创建 `project.config.json` 项目配置文件
- [ ] 创建 `sitemap.json` 站点地图配置

**执行指令：**
```
请帮我创建一个微信小程序项目，项目名为 health-uplink，包含基础的 app.js、app.json、app.wxss 和项目配置文件。项目用于健康信息收集，患者可以提交个人信息和上传图片。
```

#### 2. 配置小程序基本信息
- [ ] 配置 `app.json` 中的页面路径和导航
- [ ] 设置小程序名称、版本等基本信息
- [ ] 配置全局样式 `app.wxss`
- [ ] 设置页面导航栏样式和颜色主题

**执行指令：**
```
请配置 app.json，包含以下页面：首页(index)、患者信息页面(patient)、图片上传页面(upload)。设置导航栏标题为"健康信息收集"，主题色为医疗蓝色 #1890ff。
```

### 阶段二：工具类和API接口封装

#### 3. 创建工具类目录和API封装
- [ ] 创建 `utils` 目录
- [ ] 创建 `utils/api.js` API接口封装文件
- [ ] 封装网络请求方法
- [ ] 配置后端接口地址
- [ ] 封装患者信息提交接口
- [ ] 封装图片上传接口

**执行指令：**
```
请在 utils 目录下创建 api.js 文件，封装微信小程序的网络请求方法。包含：
1. 基础请求方法 request()
2. 患者信息提交接口 submitPatientInfo()
3. 图片上传接口 uploadImage()
后端地址暂时设为 'http://localhost:3000'
```

#### 4. 创建通用组件
- [ ] 创建 `components` 目录
- [ ] 创建导航栏组件 `navigation-bar`
- [ ] 创建加载组件
- [ ] 创建提示组件

**执行指令：**
```
请创建一个自定义导航栏组件 navigation-bar，包含返回按钮和标题显示功能，样式要符合医疗应用的简洁风格。
```

### 阶段三：页面开发

#### 5. 开发首页 (pages/index)
- [ ] 创建首页目录和文件
- [ ] 设计首页布局（患者信息入口、图片上传入口）
- [ ] 添加页面跳转逻辑
- [ ] 美化页面样式
- [ ] 添加页面动画效果

**执行指令：**
```
请开发首页，包含两个主要功能入口：
1. "填写患者信息" 按钮，点击跳转到患者信息页面
2. "上传图片资料" 按钮，点击跳转到图片上传页面
页面要有友好的医疗主题设计，使用卡片式布局，添加适当的图标。
```

#### 6. 开发患者信息页面 (pages/patient)
- [ ] 创建患者信息页面目录和文件
- [ ] 设计表单布局（姓名、年龄、病史、症状）
- [ ] 实现表单数据绑定
- [ ] 添加表单验证逻辑
- [ ] 实现数据提交功能
- [ ] 添加提交成功/失败提示
- [ ] 美化表单样式

**执行指令：**
```
请开发患者信息填写页面，包含以下字段：
- 姓名（必填，文本输入）
- 年龄（必填，数字输入）
- 病史（多行文本输入）
- 症状（多行文本输入）
要有完整的表单验证，提交按钮，以及友好的用户提示。页面样式要清晰易用。
```

#### 7. 开发图片上传页面 (pages/upload)
- [ ] 创建图片上传页面目录和文件
- [ ] 实现图片选择功能
- [ ] 实现图片预览功能
- [ ] 实现多图片上传
- [ ] 添加图片删除功能
- [ ] 实现图片上传进度显示
- [ ] 添加上传成功/失败提示
- [ ] 美化上传界面

**执行指令：**
```
请开发图片上传页面，功能包括：
1. 点击选择图片（支持多选）
2. 图片预览网格显示
3. 单个图片删除功能
4. 批量上传功能
5. 上传进度显示
6. 支持检查单、患处照片等不同类型标注
界面要直观易用，有清晰的操作指引。
```

### 阶段四：功能完善和优化

#### 8. 添加数据校验和错误处理
- [x] 完善所有表单的数据校验
- [x] 添加网络请求错误处理
- [x] 实现友好的错误提示
- [x] 添加加载状态显示
- [x] 实现数据本地缓存

**执行指令：**
```
请为整个小程序添加完善的错误处理机制：
1. 表单数据验证和错误提示
2. 网络请求失败处理
3. 图片上传失败重试机制
4. 友好的加载状态显示
5. 关键数据的本地临时存储
```

#### 9. 优化用户体验
- [x] 添加页面间的转场动画
- [x] 优化页面加载速度
- [x] 添加操作反馈效果
- [x] 实现页面状态保持
- [x] 添加操作引导

**执行指令：**
```
请优化小程序的用户体验：
1. 添加页面切换的平滑动画
2. 按钮点击的反馈效果
3. 长操作的加载动画
4. 空状态的友好提示
5. 操作成功的庆祝动效
```

#### 10. 响应式设计和适配
- [x] 适配不同屏幕尺寸
- [x] 优化不同设备的显示效果
- [x] 测试在不同机型上的表现
- [x] 优化触摸操作体验

**执行指令：**
```
请对小程序进行响应式设计优化，确保在不同尺寸的手机屏幕上都能良好显示，特别注意适配 iPhone 和 Android 设备的差异。
```

---

## 第二部分：后端管理系统开发

### 阶段一：后端项目初始化

#### 1. 创建后端项目基础结构
- [ ] 创建 `backend` 目录
- [ ] 初始化 Node.js 项目 (`npm init`)
- [ ] 创建项目目录结构（controllers、models、routes、services等）
- [ ] 安装基础依赖包
- [ ] 创建 `app.js` 主入口文件

**执行指令：**
```
请创建一个 Node.js + Express 后端项目，项目目录为 backend，包含以下结构：
- app.js (主入口)
- config/ (配置文件)
- controllers/ (控制器)
- models/ (数据模型)
- routes/ (路由)
- services/ (业务逻辑)
- middlewares/ (中间件)
- utils/ (工具类)
安装需要的依赖：express, mysql2, sequelize, multer, jsonwebtoken, cors, bcryptjs, dotenv
```

#### 2. 配置数据库连接
- [ ] 创建 `config/config.js` 数据库配置文件
- [ ] 配置 MySQL 连接参数
- [ ] 创建 Sequelize 实例
- [ ] 测试数据库连接

**执行指令：**
```
请创建数据库配置文件，连接到 MySQL 数据库：
IP: ************
端口: 3306
用户名: root
密码: zn123
数据库名: health_db
使用 Sequelize ORM，并创建数据库连接测试功能。
```

#### 3. 创建数据库模型
- [ ] 创建患者信息模型 `models/patient.js`
- [ ] 创建图片资料模型 `models/image.js`
- [ ] 创建医生信息模型 `models/doctor.js`
- [ ] 设置模型关联关系
- [ ] 创建数据库同步脚本

**执行指令：**
```
请根据设计方案创建 Sequelize 数据模型：
1. Patient 模型：patient_id, name, age, medical_history, symptoms, created_at, updated_at
2. Image 模型：image_id, patient_id, image_url, image_type, created_at
3. Doctor 模型：doctor_id, name, created_at
设置正确的关联关系：Patient hasMany Images, Image belongsTo Patient
```

### 阶段二：API 接口开发

#### 4. 创建患者信息接口
- [ ] 创建患者控制器 `controllers/patient.js`
- [ ] 创建患者服务 `services/patient.js`
- [ ] 创建患者路由 `routes/patient.js`
- [ ] 实现患者信息提交接口 POST `/api/patient/info`
- [ ] 实现患者信息查询接口 GET `/api/patient/list`
- [ ] 实现患者详情接口 GET `/api/patient/:id`

**执行指令：**
```
请创建患者信息相关的 API 接口：
1. POST /api/patient/info - 提交患者信息
2. GET /api/patient/list - 获取患者列表（支持分页、搜索）
3. GET /api/patient/:id - 获取患者详情
包含完整的控制器、服务层和路由，添加数据验证和错误处理。
```

#### 5. 创建图片上传接口
- [ ] 配置 Multer 文件上传中间件
- [ ] 创建图片控制器 `controllers/image.js`
- [ ] 创建图片服务 `services/image.js`
- [ ] 创建图片路由 `routes/image.js`
- [ ] 实现图片上传接口 POST `/api/image/upload`
- [ ] 实现图片列表接口 GET `/api/image/list`
- [ ] 实现图片删除接口 DELETE `/api/image/:id`

**执行指令：**
```
请创建图片管理相关的 API 接口：
1. POST /api/image/upload - 上传图片（使用 Multer）
2. GET /api/image/list - 获取图片列表
3. DELETE /api/image/:id - 删除图片
4. GET /api/image/:id - 获取图片详情
配置文件上传到 uploads/ 目录，支持常见图片格式，添加文件大小限制。
```

#### 6. 创建数据导出接口
- [ ] 创建导出控制器 `controllers/export.js`
- [ ] 创建导出服务 `services/export.js`
- [ ] 创建导出路由 `routes/export.js`
- [ ] 实现 CSV 导出功能
- [ ] 实现 ZIP 打包功能
- [ ] 实现导出接口 GET `/api/export/data`

**执行指令：**
```
请创建数据导出功能：
1. GET /api/export/csv - 导出患者信息为 CSV 格式
2. GET /api/export/images - 导出图片为 ZIP 压缩包
3. GET /api/export/all - 导出完整数据包（CSV + 图片ZIP）
使用 csv-writer 生成 CSV，使用 archiver 创建 ZIP 文件。
```

#### 7. 创建用户认证接口
- [ ] 创建用户控制器 `controllers/user.js`
- [ ] 创建用户服务 `services/user.js`
- [ ] 创建用户路由 `routes/user.js`
- [ ] 实现登录接口 POST `/api/user/login`
- [ ] 实现权限验证中间件 `middlewares/auth.js`
- [ ] 实现 JWT Token 生成和验证

**执行指令：**
```
请创建用户认证系统：
1. POST /api/user/login - 医生登录接口
2. 创建 JWT 认证中间件
3. 密码加密存储（使用 bcryptjs）
4. Token 验证和权限控制
5. 添加默认管理员账户的创建脚本
```

### 阶段三：前端管理界面开发

#### 8. 创建前端管理项目
- [ ] 在 `backend` 目录下创建 `frontend` 目录
- [ ] 初始化 Vue 3 + Vite 项目
- [ ] 安装 Element Plus、Vue Router、Axios 等依赖
- [ ] 配置项目基础结构
- [ ] 创建主要页面布局

**执行指令：**
```
请在 backend 目录下创建 Vue 3 + Vite 前端项目 frontend：
1. 使用 Vue 3 + Vite 脚手架
2. 安装 Element Plus UI 组件库
3. 安装 Vue Router、Axios、@element-plus/icons-vue
4. 创建基础的布局结构和路由配置
5. 配置 Axios 基础请求拦截器
```

#### 9. 开发登录和主布局
- [ ] 创建登录页面 `views/Login.vue`
- [ ] 实现登录表单和逻辑
- [ ] 创建主布局 `layout/MainLayout.vue`
- [ ] 创建侧边栏导航和顶部导航栏
- [ ] 实现路由守卫和权限控制

**执行指令：**
```
请创建登录页面和主布局：
1. 登录页面使用 Element Plus 表单组件，包含用户名密码验证
2. 主布局包含左侧导航菜单（患者管理、图片管理、数据导出）
3. 顶部导航栏显示用户信息和退出登录
4. 添加路由守卫，未登录用户重定向到登录页
5. 响应式设计，适配不同屏幕尺寸
```

#### 10. 开发业务页面
- [ ] 创建患者管理页面 `views/PatientList.vue` 和 `views/PatientDetail.vue`
- [ ] 创建图片管理页面 `views/ImageList.vue`
- [ ] 创建数据导出页面 `views/DataExport.vue`
- [ ] 实现数据表格、分页、搜索、筛选功能
- [ ] 实现图片预览、删除功能
- [ ] 实现数据导出和下载功能

**执行指令：**
```
请创建业务管理页面：
1. 患者管理：列表页面使用 Table 组件，支持搜索、分页，详情页显示完整信息
2. 图片管理：网格展示图片，支持预览、删除、按患者筛选
3. 数据导出：条件选择、导出类型选择、进度显示、下载链接
4. 所有页面都要有完善的错误处理和用户反馈
```

### 阶段四：系统优化和完善

#### 11. 添加系统监控和日志
- [ ] 集成日志系统 (winston)
- [ ] 添加 API 访问日志
- [ ] 实现错误日志记录
- [ ] 添加系统状态监控
- [ ] 创建日志查看页面

**执行指令：**
```
请添加日志和监控功能：
1. 使用 winston 添加日志系统
2. 记录 API 访问日志、错误日志
3. 创建日志查看页面
4. 添加系统状态监控（CPU、内存、磁盘）
5. 实现日志文件轮转和清理
```

#### 12. 数据库优化和安全加固
- [ ] 添加数据库索引
- [ ] 实现数据库备份功能
- [ ] 实现 API 访问频率限制
- [ ] 添加输入数据验证和XSS防护
- [ ] 实现文件上传安全检查

**执行指令：**
```
请优化数据库性能和加强安全性：
1. 为常用查询字段添加索引，配置数据库连接池
2. 实现数据库自动备份脚本
3. 使用 express-rate-limit 限制 API 访问频率
4. 添加输入数据 XSS 和 SQL 注入防护
5. 文件上传类型和大小限制，实现操作审计日志
```

### 阶段五：测试和部署

#### 13. 创建测试套件
- [ ] 安装测试框架 (Jest, Supertest)
- [ ] 编写 API 接口测试
- [ ] 编写数据库操作测试
- [ ] 创建测试数据和模拟环境
- [ ] 执行完整功能测试

**执行指令：**
```
请创建测试套件：
1. 使用 Jest 和 Supertest 编写 API 测试
2. 测试所有主要接口的正常和异常情况
3. 测试数据库操作和数据验证
4. 测试文件上传和下载功能
5. 创建测试数据初始化脚本
```

#### 14. 性能优化和部署准备
- [ ] 实现 API 响应缓存
- [ ] 优化图片处理性能
- [ ] 创建 Docker 配置文件
- [ ] 编写部署脚本
- [ ] 创建 Nginx 配置

**执行指令：**
```
请进行性能优化和部署准备：
1. 使用 Redis 实现 API 响应缓存
2. 图片上传后自动压缩和生成缩略图
3. 编写 Dockerfile 和 docker-compose.yml
4. 创建生产环境配置文件和 Nginx 反向代理配置
5. 编写自动化部署脚本
```

---

## 第三部分：项目集成和文档

### 15. 完善项目文档
- [ ] 创建项目 README.md
- [ ] 编写 API 接口文档
- [ ] 创建部署指南
- [ ] 编写用户使用手册
- [ ] 创建故障排除指南

**执行指令：**
```
请创建完整的项目文档：
1. 项目 README.md 包含简介、功能特性、环境搭建指南
2. 使用 Swagger 生成 API 接口文档
3. 编写详细的部署和配置指南
4. 创建管理员用户使用手册
5. 编写常见问题和故障排除指南
6. 系统维护和升级指导
```

### 16. 最终测试和验收
- [ ] 执行完整的系统集成测试
- [ ] 进行性能压力测试
- [ ] 验证数据安全性
- [ ] 测试用户使用流程
- [ ] 准备发布版本

**执行指令：**
```
请帮我创建最终测试检查清单：
1. 小程序所有功能完整性测试
2. 后端 API 接口压力测试
3. 前端管理界面用户体验测试
4. 数据安全和权限验证测试
5. 跨浏览器和设备兼容性测试
6. 创建发布前的最终检查项目
```

---

## 开发执行建议

### 项目启动顺序
1. **第1-2周**：后端基础框架 + 数据库设计 + API开发
2. **第3周**：前端管理界面开发
3. **第4周**：小程序开发
4. **第5周**：系统集成 + 完整测试
5. **第6周**：优化部署 + 文档完善

### 每日执行建议
- **上午**：集中时间完成一个完整模块
- **下午**：测试验证上午的开发成果
- **晚上**：文档更新和代码审查

### 与 Cline 协作技巧
1. **具体指令**：提供详细的功能需求和技术要求
2. **分步验证**：每完成一个模块立即测试
3. **代码审查**：要求解释代码逻辑和潜在问题

### 常见问题预防
1. **数据库连接**：确保MySQL服务启动和网络连接
2. **跨域问题**：正确配置CORS和代理
3. **文件上传**：确保目录权限和大小限制
4. **小程序配置**：正确设置AppID和合法域名

### 质量保证检查点
- [ ] 所有函数都有详细注释
- [ ] 错误处理覆盖所有异常情况
- [ ] 代码符合规范，没有硬编码配置
- [ ] 所有API接口正常工作
- [ ] 前端页面响应式适配
- [ ] 数据验证和安全检查到位
- [ ] 用户体验流程简单直观

---

## 紧急问题处理指南

### 开发过程中常见问题

**数据库连接失败**
```bash
# 检查MySQL服务状态
systemctl status mysql
# 测试数据库连接
mysql -h ************ -u root -p
```

**API接口500错误**
- 检查控制台错误日志
- 验证请求参数格式
- 确认数据库表结构正确

**前端页面空白**
- 检查浏览器控制台错误
- 验证API接口返回数据
- 确认路由配置正确

**小程序网络请求失败**
- 检查小程序合法域名配置
- 验证后端CORS配置
- 确认请求URL格式正确

### 项目维护和扩展

**日常维护任务**
- 每日：检查系统运行日志、监控数据库性能、备份重要数据
- 每周：清理过期日志文件、更新系统安全补丁、检查API访问统计
- 每月：完整系统备份、性能评估报告、用户反馈收集

**系统扩展方向**
- 功能扩展：患者预约系统、在线咨询功能、报告生成系统
- 技术升级：引入Redis缓存、实现微服务架构、添加消息队列
- 安全加强：双因子认证、数据加密传输、访问行为分析

```
 根据 `health-uplink/health_uplink_todo.md` 文件内容和当前工作区目录文件，汇总的最终项目文件目录结构如下：

 
health-uplink/
├── .eslintrc.js
├── app.js
├── app.json
├── app.wxss
├── health_uplink_todo.md
├── project.config.json
├── project.private.config.json
├── README.md
├── sitemap.json
├── 需求说明.txt
├── components/
│   └── navigation-bar/
│       ├── navigation-bar.js
│       ├── navigation-bar.json
│       ├── navigation-bar.wxml
│       └── navigation-bar.wxss
├── pages/
│   ├── index/
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   ├── patient/
│   │   ├── patient.js
│   │   ├── patient.json
│   │   ├── patient.wxml
│   │   └── patient.wxss
│   └── upload/
│       ├── upload.js
│       ├── upload.json
│       ├── upload.wxml
│       └── upload.wxss
├── utils/
│   └── api.js
├── backend/
│   ├── app.js
│   ├── config/
│   │   └── config.js
│   ├── controllers/
│   │   ├── export.js
│   │   ├── image.js
│   │   ├── patient.js
│   │   └── user.js
│   ├── models/
│   │   ├── doctor.js
│   │   ├── image.js
│   │   └── patient.js
│   ├── routes/
│   │   ├── export.js
│   │   ├── image.js
│   │   ├── patient.js
│   │   └── user.js
│   ├── services/
│   │   ├── export.js
│   │   ├── image.js
│   │   ├── patient.js
│   │   └── user.js
│   ├── middlewares/
│   │   └── auth.js
│   ├── utils/
│   └── frontend/
│       ├── src/
│       │   ├── main.js
│       │   ├── App.vue
│       │   ├── router/
│       │   │   └── index.js
│       │   ├── api/
│       │   │   └── index.js
│       │   ├── layout/
│       │   │   └── MainLayout.vue
│       │   └── views/
│       │       ├── DataExport.vue
│       │       ├── ImageList.vue
│       │       ├── Login.vue
│       │       ├── PatientDetail.vue
│       │       └── PatientList.vue
│       └── package.json
└── docs/
    ├── README.md
    ├── API_Documentation.md
    ├── Deployment_Guide.md
    ├── User_Manual.md
    └── Troubleshooting_Guide.md
```
