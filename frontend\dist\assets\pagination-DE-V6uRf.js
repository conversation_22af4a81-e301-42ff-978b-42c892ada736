const S={TABLE:{DEFAULT_PAGE_SIZE:20,PAGE_SIZES:[10,20,50,100],LAYOUT:"total, sizes, prev, pager, next, jumper"},GRID:{DEFAULT_PAGE_SIZE:24,PAGE_SIZES:[12,24,48,96],LAYOUT:"total, sizes, prev, pager, next, jumper"},WIDGET:{DEFAULT_PAGE_SIZE:10,PAGE_SIZES:[5,10,20,50],LAYOUT:"prev, pager, next"}},p={PATIENT_LIST:{type:"TABLE",pageSize:20,pageSizes:[10,20,50,100]},DOCTOR_LIST:{type:"TABLE",pageSize:20,pageSizes:[10,20,50,100]},IMAGE_LIST:{type:"GRID",pageSize:24,pageSizes:[12,24,48,96]},MEDICAL_RECORD_LIST:{type:"TABLE",pageSize:15,pageSizes:[10,15,30,50]},SYMPTOM_RECORD_LIST:{type:"TABLE",pageSize:20,pageSizes:[10,20,50,100]}};function a(A){const e=p[A];if(!e)return console.warn(`未找到页面 ${A} 的分页配置，使用默认配置`),{page:1,pageSize:S.TABLE.DEFAULT_PAGE_SIZE,total:0,pageSizes:S.TABLE.PAGE_SIZES};const E=S[e.type];return{page:1,pageSize:e.pageSize||E.DEFAULT_PAGE_SIZE,total:0,pageSizes:e.pageSizes||E.PAGE_SIZES,layout:E.LAYOUT}}export{a as g};
