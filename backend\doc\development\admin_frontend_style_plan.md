# 后端管理界面风格和布局统一化计划

## 目标

实现后端管理界面的整体风格和布局统一，提高代码可维护性，并为后续新页面开发提供一致的基础。

## 当前问题分析

- 后端管理界面位于 `frontend/src/` 目录下。
- 使用 Element Plus 组件库和 Vue.js。
- `MainLayout.vue` 提供了基本的侧边栏布局框架。
- 各个业务页面组件（`src/views/` 下的 `.vue` 文件）在其 `<style scoped>` 块中定义了一些重复的布局和样式，导致不一致和维护困难。
- `src/style.css` 包含基础全局样式，但不足以覆盖所有统一需求。

## 计划步骤

1.  **建立统一的样式基础**:
    *   在全局或专门的后端管理样式文件（例如 `src/admin-styles.css`）中定义通用的 CSS 变量（用于颜色、间距、字体大小等）和基础样式。
    *   将页面中重复出现的布局样式（如内容区域的内边距、最大宽度、居中）提取为通用的 CSS 类或直接应用于布局组件。

2.  **创建可复用的布局组件**:
    *   创建一个新的 Vue 组件，例如 `ContentContainer.vue`，用于包裹每个业务页面的主要内容区域。这个组件将负责应用统一的内容区域样式（内边距、最大宽度、居中等）。
    *   如果多个页面有相似的表单或表格结构，可以考虑创建更细粒度的通用组件来封装这些模式。

3.  **重构现有业务页面**:
    *   修改 `src/views/` 目录下的现有业务页面组件，移除其 `<style scoped>` 中重复的布局样式。
    *   在这些页面中使用新创建的 `ContentContainer.vue` 组件来包裹其内容。
    *   调整页面内部元素的样式，使其依赖于全局样式、CSS 变量或通用组件。

4.  **文档化**:
    *   编写简单的文档，说明新的样式规范、CSS 变量的使用以及通用组件的功能和用法，以便团队成员遵循。

## 组件结构示意图

```mermaid
graph TD
    A[MainLayout] --> B[el-aside];
    A --> C[el-container];
    C --> D[el-header];
    C --> E[el-main];
    E --> F[ContentContainer];
    F --> G[业务页面组件<br/>(PatientList, ImageList等)];
```

## 后续行动

在您确认计划并希望开始实施后，我们将切换到 `code` 模式来执行上述步骤。