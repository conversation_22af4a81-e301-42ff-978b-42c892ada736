import subprocess
import os

# This script assumes it is located in the 'health-uplink/scripts' directory.
# It starts the backend server and the frontend development server in new console windows.

# Get the absolute path of the directory containing this script.
script_dir = os.path.dirname(os.path.abspath(__file__))
# Get the project root directory ('backend') by going one level up.
project_root = os.path.dirname(script_dir)
workspace_root = os.path.dirname(project_root)

# Define the working directories for the backend and frontend.
backend_dir = project_root
frontend_dir = os.path.join(workspace_root, 'frontend')

# Commands to be executed.
backend_command = 'node app.js'
frontend_command = 'npm run dev'

print("Starting backend and frontend servers...")
print(f"Backend directory: {backend_dir}")
print(f"Frontend directory: {frontend_dir}")

try:
    # Start the backend server in a new console window.
    # The `start` command in `cmd` is used to open a new window.
    # `cmd /k` keeps the window open after the command finishes.
    subprocess.Popen(f'start cmd /k "cd /d {backend_dir} && {backend_command}"', shell=True)
    print("Backend server startup command issued.")

    # Start the frontend dev server in a new console window.
    subprocess.Popen(f'start cmd /k "cd /d {frontend_dir} && {frontend_command}"', shell=True)
    print("Frontend server startup command issued.")

    print("\nBoth startup commands have been executed.")
    print("Please check the new command prompt windows for output from the servers.")

except Exception as e:
    print(f"An error occurred: {e}")