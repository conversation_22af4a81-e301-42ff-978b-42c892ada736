import{_ as N,d as T,c as y,o as p,w as a,b as o,a as s,j as P,k as S,r as m,e as i,t as r,l as U,m as C,F as L,n as R,C as j,f as k,g as G,p as K,h as W,s as z,E as l,u as q,i as E}from"./index-D1pIs7PZ.js";const A=T({name:"PatientDetail",components:{ContentContainer:j},setup(){const e=K(),n=q(),g=k(!1),_=k({}),b=k(!1),w=k(null),u=G(()=>[{title:"首页",path:"/"},{title:"患者管理",path:"/patients"},{title:"患者详情",path:`/patient/${e.params.id}`}]),f=async()=>{g.value=!0;try{const t=await z.get(`/patient/${e.params.id}`);t.patient?_.value=t.patient:l.error("获取患者详情失败")}catch(t){console.error("获取患者详情失败:",t),l.error("获取患者详情失败，请稍后重试")}finally{g.value=!1}},d=()=>{n.push(`/patient/${e.params.id}/edit`)},I=async()=>{try{await E.confirm(`确定要删除患者 "${_.value.name}" 的信息吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await z.delete(`/patient/${e.params.id}`)).message?(l.success("患者信息删除成功"),n.push("/patients")):l.error("删除失败")}catch(t){t!=="cancel"&&(console.error("删除患者失败:",t),l.error("删除失败，请稍后重试"))}},h=()=>{l.info("上传图片功能开发中...")},D=t=>t?t.startsWith("http")?t:`http://your-production-api-url.com${t}`:"",$=t=>{w.value=t,b.value=!0},B=async t=>{try{await E.confirm("确定要删除这张图片吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await z.delete(`/image/${t}`)).success?(l.success("图片删除成功"),f()):l.error("删除图片失败")}catch(c){c!=="cancel"&&(console.error("删除图片失败:",c),l.error("删除图片失败，请稍后重试"))}},V=t=>t?new Date(t).toLocaleString("zh-CN"):"-",v=t=>{if(!t)return"0 B";const c=1024,F=["B","KB","MB","GB"],M=Math.floor(Math.log(t)/Math.log(c));return parseFloat((t/Math.pow(c,M)).toFixed(2))+" "+F[M]};return W(()=>{f()}),{loading:g,patient:_,breadcrumbs:u,dialogVisible:b,currentImage:w,handleEdit:d,handleDelete:I,handleUploadImage:h,getImageUrl:D,previewImage:$,deleteImage:B,formatDate:V,formatFileSize:v}}}),H={class:"patient-info-section"},J={class:"card-header"},O={class:"text-content"},Q={class:"text-content"},X={class:"patient-images-section"},Y={class:"card-header"},Z={key:0,class:"images-grid"},x=["src","alt","onClick"],ee={class:"image-info"},te={class:"image-name"},ae={class:"image-meta"},ne={class:"image-actions"},oe={class:"image-preview-container"},se=["src","alt"];function ie(e,n,g,_,b,w){const u=m("el-button"),f=m("el-tag"),d=m("el-descriptions-item"),I=m("el-descriptions"),h=m("el-card"),D=m("el-empty"),$=m("el-dialog"),B=m("ContentContainer"),V=S("loading");return p(),y(B,{title:`患者详情 - ${e.patient.name||"加载中..."}`,description:"查看患者的详细信息和相关医疗记录","show-header":!0,breadcrumbs:e.breadcrumbs,"show-breadcrumb":!0},{"header-right":a(()=>[s(u,{type:"primary",onClick:e.handleEdit,icon:"Edit"},{default:a(()=>n[1]||(n[1]=[i(" 编辑 ")])),_:1,__:[1]},8,["onClick"]),s(u,{type:"danger",onClick:e.handleDelete,icon:"Delete"},{default:a(()=>n[2]||(n[2]=[i(" 删除 ")])),_:1,__:[2]},8,["onClick"])]),default:a(()=>{var v;return[o("div",H,[P((p(),y(h,null,{header:a(()=>[o("div",J,[n[3]||(n[3]=o("span",null,"基本信息",-1)),e.patient.patient_id?(p(),y(f,{key:0,type:"info"},{default:a(()=>[i(" ID: "+r(e.patient.patient_id),1)]),_:1})):U("",!0)])]),default:a(()=>[s(I,{column:2,border:""},{default:a(()=>[s(d,{label:"姓名"},{default:a(()=>[i(r(e.patient.name||"-"),1)]),_:1}),s(d,{label:"年龄"},{default:a(()=>[i(r(e.patient.age||"-")+" 岁 ",1)]),_:1}),s(d,{label:"病史",span:"2"},{default:a(()=>[o("div",O,r(e.patient.medical_history||"无"),1)]),_:1}),s(d,{label:"症状",span:"2"},{default:a(()=>[o("div",Q,r(e.patient.symptoms||"无"),1)]),_:1}),s(d,{label:"创建时间"},{default:a(()=>[i(r(e.formatDate(e.patient.created_at)),1)]),_:1}),s(d,{label:"更新时间"},{default:a(()=>[i(r(e.formatDate(e.patient.updated_at)),1)]),_:1})]),_:1})]),_:1})),[[V,e.loading]])]),o("div",X,[s(h,null,{header:a(()=>[o("div",Y,[n[5]||(n[5]=o("span",null,"相关图片",-1)),s(u,{type:"primary",size:"small",onClick:e.handleUploadImage,icon:"Upload"},{default:a(()=>n[4]||(n[4]=[i(" 上传图片 ")])),_:1,__:[4]},8,["onClick"])])]),default:a(()=>[e.patient.images&&e.patient.images.length>0?(p(),C("div",Z,[(p(!0),C(L,null,R(e.patient.images,t=>(p(),C("div",{key:t.image_id,class:"image-item"},[o("img",{src:e.getImageUrl(t.image_url),alt:t.original_name,class:"image-preview",onClick:c=>e.previewImage(t)},null,8,x),o("div",ee,[o("div",te,r(t.original_name||"未知文件"),1),o("div",ae,r(e.formatFileSize(t.file_size))+" | "+r(e.formatDate(t.created_at)),1),o("div",ne,[s(u,{link:"",size:"small",onClick:c=>e.deleteImage(t.image_id),class:"text-error"},{default:a(()=>n[6]||(n[6]=[i(" 删除 ")])),_:2,__:[6]},1032,["onClick"])])])]))),128))])):(p(),y(D,{key:1,description:"该患者暂无图片资料","image-size":100},{default:a(()=>[s(u,{type:"primary",onClick:e.handleUploadImage},{default:a(()=>n[7]||(n[7]=[i(" 上传第一张图片 ")])),_:1,__:[7]},8,["onClick"])]),_:1}))]),_:1})]),s($,{modelValue:e.dialogVisible,"onUpdate:modelValue":n[0]||(n[0]=t=>e.dialogVisible=t),title:((v=e.currentImage)==null?void 0:v.original_name)||"图片预览",width:"80%",center:""},{default:a(()=>[o("div",oe,[e.currentImage?(p(),C("img",{key:0,src:e.getImageUrl(e.currentImage.image_url),alt:e.currentImage.original_name,class:"preview-image"},null,8,se)):U("",!0)])]),_:1},8,["modelValue","title"])]}),_:1},8,["title","breadcrumbs"])}const le=N(A,[["render",ie],["__scopeId","data-v-3644d4aa"]]);export{le as default};
