import{_ as V,d as h,c as _,o as f,w as l,b as s,a as o,l as w,r as n,e as v,t as F,C as N,f as m,s as U}from"./index-D1pIs7PZ.js";const B=h({components:{ContentContainer:N},setup(){const t=m({type:"csv"}),e=m(!1),p=m(""),d=m("");return{exportForm:t,loading:e,exportUrl:p,exportFileName:d,handleExport:async()=>{e.value=!0,p.value="",d.value="";try{let r="",a="";switch(t.value.type){case"csv":r="/export/csv",a="patients.csv";break;case"images":r="/export/images",a="images.zip";break;case"all":r="/export/all",a="health_data_full.zip";break;default:alert("请选择有效的导出类型。"),e.value=!1;return}const i=await U.get(r,{responseType:"blob"}),u=new Blob([i],{type:i.type});p.value=URL.createObjectURL(u),d.value=a}catch(r){console.error("导出失败:",r),alert("导出失败，请稍后再试。")}finally{e.value=!1}}}}});function E(t,e,p,d,b,r){const a=n("el-option"),i=n("el-select"),u=n("el-form-item"),y=n("el-button"),x=n("el-form"),c=n("el-card"),C=n("el-link"),g=n("ContentContainer");return f(),_(g,null,{default:l(()=>[e[5]||(e[5]=s("h2",null,"数据导出",-1)),o(c,null,{header:l(()=>e[1]||(e[1]=[s("div",{class:"card-header"},[s("span",null,"导出选项")],-1)])),default:l(()=>[o(x,{model:t.exportForm,"label-width":"120px"},{default:l(()=>[o(u,{label:"导出类型"},{default:l(()=>[o(i,{modelValue:t.exportForm.type,"onUpdate:modelValue":e[0]||(e[0]=k=>t.exportForm.type=k),placeholder:"请选择导出类型"},{default:l(()=>[o(a,{label:"患者信息 (CSV)",value:"csv"}),o(a,{label:"图片资料 (ZIP)",value:"images"}),o(a,{label:"完整数据包 (CSV + 图片ZIP)",value:"all"})]),_:1},8,["modelValue"])]),_:1}),o(u,null,{default:l(()=>[o(y,{type:"primary",onClick:t.handleExport,loading:t.loading},{default:l(()=>e[2]||(e[2]=[v("开始导出")])),_:1,__:[2]},8,["onClick","loading"])]),_:1})]),_:1},8,["model"])]),_:1}),t.exportUrl?(f(),_(c,{key:0,style:{"margin-top":"20px"}},{header:l(()=>e[3]||(e[3]=[s("div",{class:"card-header"},[s("span",null,"导出结果")],-1)])),default:l(()=>[e[4]||(e[4]=s("p",null,"导出已完成。点击下方链接下载：",-1)),o(C,{href:t.exportUrl,type:"primary",target:"_blank"},{default:l(()=>[v(F(t.exportFileName),1)]),_:1},8,["href"])]),_:1,__:[4]})):w("",!0)]),_:1,__:[5]})}const I=V(B,[["render",E],["__scopeId","data-v-2b0c99b9"]]);export{I as default};
