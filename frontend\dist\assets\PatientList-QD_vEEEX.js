import{_ as z,d as E,c as P,o as V,w as i,a as d,b as C,r as m,e as v,D as R,S as B,C as F,f as g,g as _,h as N,s as S,E as c,u as I,i as L}from"./index-D1pIs7PZ.js";import{g as M}from"./pagination-DE-V6uRf.js";const O=E({components:{ContentContainer:F,SearchForm:B,DataTable:R},setup(){const a=I(),n=g([]),p=g(!1),h=g({}),t=g(M("PATIENT_LIST")),w=_(()=>[{title:"首页",path:"/"},{title:"患者管理",path:"/patients"}]),f=_(()=>[{key:"search",label:"关键词",type:"input",placeholder:"请输入姓名、病史或症状",clearable:!0},{key:"age_min",label:"最小年龄",type:"input",placeholder:"最小年龄",clearable:!0},{key:"age_max",label:"最大年龄",type:"input",placeholder:"最大年龄",clearable:!0},{key:"date_range",label:"创建时间",type:"daterange",placeholder:"选择时间范围",clearable:!0}]),s=_(()=>[{prop:"patient_id",label:"ID",width:80,align:"center"},{prop:"name",label:"姓名",minWidth:100,showOverflowTooltip:!0},{prop:"age",label:"年龄",width:80,align:"center"},{prop:"medical_history",label:"病史",minWidth:150,showOverflowTooltip:!0,formatter:e=>e.medical_history||"无"},{prop:"symptoms",label:"症状",minWidth:150,showOverflowTooltip:!0,formatter:e=>e.symptoms||"无"},{prop:"created_at",label:"创建时间",width:180,align:"center",formatter:e=>e.created_at?new Date(e.created_at).toLocaleString("zh-CN"):"-"}]),o=async()=>{p.value=!0;try{const e={page:t.value.page,limit:t.value.pageSize,...h.value},l=await S.get("/patient/list",{params:e});l.patients?(n.value=l.patients,t.value.total=l.total):c.error(l.message||"获取患者列表失败")}catch(e){console.error("获取患者列表失败:",e),c.error("获取患者列表失败，请稍后重试")}finally{p.value=!1}},b=e=>{h.value=e,t.value.page=1,o()},r=()=>{h.value={},t.value.page=1,o()},u=e=>{t.value.pageSize=e,t.value.page=1,o()},k=e=>{t.value.page=e,o()},y=e=>{a.push(`/patient/${e}`)},D=e=>{y(e.patient_id)},T=e=>{a.push(`/patient/${e.patient_id}/edit`)},$=async e=>{try{await L.confirm(`确定要删除患者 "${e.name}" 的信息吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await S.delete(`/patient/${e.patient_id}`)).message?(c.success("患者信息删除成功"),o()):c.error("删除失败")}catch(l){l!=="cancel"&&(console.error("删除患者失败:",l),c.error("删除失败，请稍后重试"))}};return N(()=>{o()}),{patients:n,loading:p,pagination:t,breadcrumbs:w,searchFields:f,tableColumns:s,fetchPatients:o,handleSearch:b,handleReset:r,handleSizeChange:u,handleCurrentChange:k,viewPatientDetail:y,handleView:D,handleEdit:T,handleDelete:$}}}),W={class:"standard-list-container"},A={class:"standard-content-area"},j={class:"standard-table-container"};function q(a,n,p,h,t,w){const f=m("SearchForm"),s=m("el-button"),o=m("DataTable"),b=m("ContentContainer");return V(),P(b,{title:"患者管理",description:"管理和查看患者信息，包括基本信息、病史和症状记录","show-header":!0,breadcrumbs:a.breadcrumbs,"show-breadcrumb":!0},{default:i(()=>[d(f,{"search-fields":a.searchFields,loading:a.loading,onSearch:a.handleSearch,onReset:a.handleReset},null,8,["search-fields","loading","onSearch","onReset"]),C("div",W,[C("div",A,[C("div",j,[d(o,{data:a.patients,columns:a.tableColumns,loading:a.loading,pagination:a.pagination,title:"患者列表","max-height":600,onCurrentChange:a.handleCurrentChange,onSizeChange:a.handleSizeChange,onView:a.handleView,onEdit:a.handleEdit,onDelete:a.handleDelete,onRefresh:a.fetchPatients},{actions:i(({row:r})=>[d(s,{link:"",size:"small",onClick:u=>a.viewPatientDetail(r.patient_id),icon:"View"},{default:i(()=>n[0]||(n[0]=[v(" 详情 ")])),_:2,__:[0]},1032,["onClick"]),d(s,{link:"",size:"small",onClick:u=>a.handleEdit(r),icon:"Edit"},{default:i(()=>n[1]||(n[1]=[v(" 编辑 ")])),_:2,__:[1]},1032,["onClick"]),d(s,{link:"",size:"small",onClick:u=>a.handleDelete(r),class:"text-error",icon:"Delete"},{default:i(()=>n[2]||(n[2]=[v(" 删除 ")])),_:2,__:[2]},1032,["onClick"])]),_:1},8,["data","columns","loading","pagination","onCurrentChange","onSizeChange","onView","onEdit","onDelete","onRefresh"])])])])]),_:1},8,["breadcrumbs"])}const J=z(O,[["render",q],["__scopeId","data-v-75e97f6d"]]);export{J as default};
