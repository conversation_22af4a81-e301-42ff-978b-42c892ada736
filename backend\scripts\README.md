# System Startup Scripts

This directory contains scripts for managing the `health-uplink` system.

## `start_system.py`

This Python script is used to start the backend server and the admin frontend development server simultaneously.

### Environment Setup

This project uses a Python virtual environment to manage dependencies for its scripts.

1.  **Activate the virtual environment:**
    Before running any scripts, navigate to the `backend` directory and activate the virtual environment.

    -   **Windows (CMD):**
        ```bash
        .venv\Scripts\activate
        ```
    -   **Windows (PowerShell):**
        ```powershell
        .venv\Scripts\Activate.ps1
        ```
    -   **macOS/Linux:**
        ```bash
        source .venv/bin/activate
        ```

2.  **Install dependencies:**
    This project uses `uv` for fast package management. If you have `uv` installed, you can install dependencies from `requirements.txt`:
    ```bash
    uv pip install -r requirements.txt
    ```
    If you don't have `uv`, you can use `pip`:
    ```bash
    pip install -r requirements.txt
    ```

### Usage

After activating the virtual environment, you can run the startup script from the `backend` directory:

```bash
python scripts/start_system.py
```

### How it Works

The script performs the following actions:
1.  It identifies the project's root directory.
2.  It opens a new terminal window for the backend server, navigates to `backend`, and runs `node app.js`.
3.  It opens another new terminal window for the frontend server, navigates to `frontend`, and runs `npm run dev`.

This allows you to see the output of both processes in separate, dedicated console windows, similar to the behavior of `start_admin.bat`.