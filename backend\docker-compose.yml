services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: health-uplink-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-health_uplink}
      MYSQL_USER: ${MYSQL_USER:-health_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-health_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    networks:
      - health-uplink-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: health-uplink-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - health-uplink-network
    command: redis-server --appendonly yes

  # 后端服务
  backend:
    image: health-uplink-backend:latest
    build:
      context: .
      dockerfile: Dockerfile
    container_name: health-uplink-backend
    restart: unless-stopped
    environment:
      NODE_ENV: docker
    ports:
      - "3000:3000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - health-uplink-network
    command: sh -c "npm run db:seed && node app.js"
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 管理后台
  frontend:
    image: health-uplink-frontend:latest
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: health-uplink-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - health-uplink-network

  # 数据库备份服务
  backup:
    image: mysql:8.0
    container_name: health-uplink-backup
    restart: "no"
    environment:
      MYSQL_HOST: mysql
      MYSQL_DATABASE: ${MYSQL_DATABASE:-health_uplink}
      MYSQL_USER: ${MYSQL_USER:-health_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-health_password}
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - health-uplink-network
    command: /scripts/backup.sh

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  health-uplink-network:
    driver: bridge
